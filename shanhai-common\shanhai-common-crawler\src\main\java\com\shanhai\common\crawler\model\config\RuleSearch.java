package com.shanhai.common.crawler.model.config;

import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 搜索页解析配置
 * <p>
 * 用于描述如何从搜索页提取书籍列表及相关参数。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleSearch extends BaseEntity {
    /**
     * 搜索接口URL
     */
    private String url;
    /**
     * 搜索参数模板
     */
    private String searchParam;
    /**
     * 书籍列表选择器
     */
    private String bookListSelector;
    /**
     * 单本书籍项选择器
     */
    private String bookItemSelector;
    /**
     * 书名选择器（CSS/JQ/JSONPath）
     */
    private String nameSelector;
    /**
     * 作者选择器
     */
    private String authorSelector;
    /**
     * 封面图片选择器
     */
    private String coverSelector;
    /**
     * 简介选择器
     */
    private String introSelector;
    /**
     * 分类选择器
     */
    private String categorySelector;
    /**
     * 字数选择器
     */
    private String wordCountSelector;
    /**
     * 最新章节选择器
     */
    private String lastChapterSelector;
    /**
     * 书籍详情页URL选择器
     */
    private String bookUrlSelector;
    /**
     * 书籍详情页URL属性名（如href、src等）
     */
    private String bookUrlAttr;
    /**
     * 等待元素选择器（可选）
     */
    private String waitForSelector;
    /**
     * 关联主表ID
     */
    private String ruleId;
}