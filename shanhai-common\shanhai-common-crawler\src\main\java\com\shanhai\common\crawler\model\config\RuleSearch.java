package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 搜索页解析配置
 * <p>
 * 用于描述如何从搜索页提取书籍列表及相关参数。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_rule_search")
@TableName("crawler_rule_search")
public class RuleSearch extends BaseEntity {
    /**
     * 搜索接口URL
     */
    @Column(name = "url", comment = "搜索接口URL", type = MySqlTypeConstant.VARCHAR, length = 500, isNull = false)
    private String url;

    /**
     * 搜索参数模板
     */
    @Column(name = "search_param", comment = "搜索参数模板", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String searchParam;

    /**
     * 书籍列表选择器
     */
    @Column(name = "book_list_selector", comment = "书籍列表选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String bookListSelector;

    /**
     * 单本书籍项选择器
     */
    @Column(name = "book_item_selector", comment = "单本书籍项选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String bookItemSelector;

    /**
     * 书名选择器（CSS/JQ/JSONPath）
     */
    @Column(name = "name_selector", comment = "书名选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String nameSelector;

    /**
     * 作者选择器
     */
    @Column(name = "author_selector", comment = "作者选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String authorSelector;

    /**
     * 封面图片选择器
     */
    @Column(name = "cover_selector", comment = "封面图片选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String coverSelector;
    /**
     * 简介选择器
     */
    @Column(name = "intro_selector", comment = "简介选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String introSelector;

    /**
     * 分类选择器
     */
    @Column(name = "category_selector", comment = "分类选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String categorySelector;

    /**
     * 字数选择器
     */
    @Column(name = "word_count_selector", comment = "字数选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String wordCountSelector;

    /**
     * 最新章节选择器
     */
    @Column(name = "last_chapter_selector", comment = "最新章节选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String lastChapterSelector;

    /**
     * 书籍详情页URL选择器
     */
    @Column(name = "book_url_selector", comment = "书籍详情页URL选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String bookUrlSelector;

    /**
     * 书籍详情页URL属性名（如href、src等）
     */
    @Column(name = "book_url_attr", comment = "书籍详情页URL属性名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true, defaultValue = "href")
    private String bookUrlAttr;

    /**
     * 等待元素选择器（可选）
     */
    @Column(name = "wait_for_selector", comment = "等待元素选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String waitForSelector;

    /**
     * 关联主表ID
     */
    @Column(name = "rule_id", comment = "关联主表ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleId;
}