package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterContentConfig {
    private String titleSelector;
    private String contentSelector;
    private String contentFilter;
    private java.util.List<com.shanhai.common.crawler.model.config.ReplaceRule> replaceRules;
    private Boolean removeHtml;
    private java.util.List<String> removeSelectors;
    private String nextChapterSelector;
    private String nextChapterAttr;
    private String prevChapterSelector;
    private String prevChapterAttr;
    private String waitForSelector;
} 