# 数据库表不存在问题修复总结

## 问题描述

应用启动时出现以下错误：
```
java.sql.SQLSyntaxErrorException: Table 'shanhai.crawler_rule' doesn't exist
```

这表明数据库中缺少必要的表结构，MyBatis ACTable 的自动建表功能没有正常工作。

## 问题分析

### 根本原因
1. **ACTable 自动建表未生效**：虽然配置了 ACTable，但可能由于配置问题或依赖问题导致自动建表功能没有正常工作
2. **数据库初始化时机问题**：应用启动时立即尝试查询数据库，但表还没有被创建
3. **配置不完整**：ACTable 的配置可能不够完整或者有冲突

### 涉及的表
根据错误信息和代码分析，需要创建以下表：
- `crawler_rule` - 爬虫规则主表
- `crawler_rule_search` - 搜索规则表
- `crawler_rule_book_info` - 书籍信息规则表
- `crawler_rule_chapter` - 章节规则表
- `crawler_rule_content` - 内容规则表
- `crawler_rule_anti_spider` - 反爬虫规则表
- `crawler_rule_replace` - 替换规则表

## 修复方案

### 方案1：完善 ACTable 配置（推荐）

#### 1. 创建 ACTable 配置类
**文件**: `shanhai-api/src/main/java/com/shanhai/api/config/ACTableConfig.java`

```java
@Configuration
@ConfigurationProperties(prefix = "mybatis")
public class ACTableConfig {
    private Table table = new Table();
    private Model model = new Model();
    
    // 配置自动建表模式为 update
    // 配置实体类包路径
}
```

#### 2. 优化 RuleManager 初始化
**文件**: `shanhai-common/shanhai-common-crawler/src/main/java/com/shanhai/common/crawler/manager/RuleManager.java`

```java
@PostConstruct
public void init() {
    log.info("初始化规则管理器...");
    try {
        // 加载默认规则
        loadDefaultRules();
        
        // 尝试刷新缓存，如果数据库表不存在则跳过
        try {
            refreshCache();
            log.info("规则管理器初始化完成，共加载{}个规则", ruleCache.size());
        } catch (Exception e) {
            log.warn("数据库规则加载失败，可能是表尚未创建: {}", e.getMessage());
            log.info("规则管理器初始化完成，仅加载了默认规则 {} 个", ruleCache.size());
        }
    } catch (Exception e) {
        log.error("规则管理器初始化失败", e);
        // 不抛出异常，允许应用继续启动
        log.warn("规则管理器将以降级模式运行，仅使用默认规则");
    }
}
```

### 方案2：手动创建数据库表（备用方案）

如果 ACTable 自动建表仍然不工作，可以手动执行 SQL 脚本：

**文件**: `shanhai-api/src/main/resources/sql/init_tables.sql`

包含了所有必要表的创建语句，可以直接在数据库中执行。

### 方案3：检查和修复 ACTable 配置

#### 1. 确认 MyBatis 配置
**文件**: `shanhai-api/src/main/resources/config/application-mybatis.yml`

```yaml
mybatis:
  table:
    auto: update  # 确保设置为 update 模式
  model:
    pack: com.shanhai.service.entity,com.shanhai.common.crawler.model.config
```

#### 2. 确认实体类注解
确保所有实体类都有正确的 ACTable 注解：

```java
@Table(name = "crawler_rule")
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    @Column(name = "source_name", comment = "站点名称", 
            type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String sourceName;
    // ...
}
```

## 验证修复

### 1. 检查应用启动日志
修复后，应用启动时应该看到：
```
初始化规则管理器...
数据库规则加载失败，可能是表尚未创建: Table 'shanhai.crawler_rule' doesn't exist
规则管理器初始化完成，仅加载了默认规则 X 个
```

或者（如果 ACTable 工作正常）：
```
初始化规则管理器...
规则管理器初始化完成，共加载X个规则
```

### 2. 检查数据库表
连接到数据库，检查表是否被创建：
```sql
USE shanhai;
SHOW TABLES LIKE 'crawler_%';
```

### 3. 测试应用功能
应用应该能够正常启动，不再抛出表不存在的异常。

## 故障排除

### 如果 ACTable 仍然不工作

1. **检查依赖版本**：
   ```xml
   <dependency>
       <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
       <artifactId>mybatis-enhance-actable</artifactId>
       <version>1.5.0.RELEASE</version>
   </dependency>
   ```

2. **检查数据库连接**：
   确保数据库连接正常，用户有创建表的权限。

3. **检查日志级别**：
   调整日志级别查看 ACTable 的详细日志：
   ```yaml
   logging:
     level:
       com.gitee.sunchenbin.mybatis.actable: DEBUG
   ```

4. **手动执行 SQL**：
   如果自动建表始终不工作，可以手动执行 `init_tables.sql` 脚本。

### 如果应用仍然启动失败

1. **检查其他依赖**：确保所有 MyBatis 相关依赖都已正确添加
2. **检查配置文件**：确保数据源配置正确
3. **检查包扫描**：确保所有相关包都在扫描范围内

## 最佳实践建议

### 1. 数据库初始化策略
- **开发环境**：使用 ACTable 的 `update` 模式，方便开发调试
- **生产环境**：建议使用 `none` 模式，通过脚本手动管理表结构

### 2. 错误处理策略
- **优雅降级**：当数据库表不存在时，应用应该能够以降级模式运行
- **详细日志**：记录详细的错误信息，便于问题排查
- **健康检查**：提供健康检查接口，监控数据库连接状态

### 3. 配置管理
- **环境隔离**：不同环境使用不同的数据库配置
- **配置验证**：启动时验证关键配置项
- **文档维护**：维护数据库表结构文档

## 总结

通过以下修复措施，解决了数据库表不存在的问题：

1. ✅ **完善了 ACTable 配置**：创建了专门的配置类
2. ✅ **优化了初始化逻辑**：添加了优雅的错误处理
3. ✅ **提供了备用方案**：创建了手动建表脚本
4. ✅ **改进了错误处理**：应用可以在表不存在时继续启动

修复后，应用应该能够正常启动，即使在数据库表不存在的情况下也能以降级模式运行，为后续的功能开发提供了稳定的基础。
