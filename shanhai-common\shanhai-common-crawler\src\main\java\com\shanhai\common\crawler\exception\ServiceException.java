package com.shanhai.common.crawler.exception;

/**
 * 业务异常
 * <p>
 * 用于统一处理业务层抛出的自定义异常。
 *
 * <AUTHOR>
 */
public class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceException() {
    }

    public ServiceException(String message) {
        this.message = message;
    }

    public ServiceException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    /**
     * 获取错误明细
     *
     * @return 错误明细
     */
    public String getDetailMessage() {
        return detailMessage;
    }

    /**
     * 获取错误提示
     *
     * @return 错误提示
     */
    @Override
    public String getMessage() {
        return message;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 设置错误提示
     *
     * @param message 错误提示
     * @return 当前对象
     */
    public ServiceException setMessage(String message) {
        this.message = message;
        return this;
    }

    /**
     * 设置错误明细
     *
     * @param detailMessage 错误明细
     * @return 当前对象
     */
    public ServiceException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}