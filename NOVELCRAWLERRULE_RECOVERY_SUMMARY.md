# NovelCrawlerRule 文件恢复总结

## 问题描述

在修复 ACTable 兼容性问题的过程中，我意外删除了 `NovelCrawlerRule.java` 文件，导致大量编译错误。

## 影响范围

删除 `NovelCrawlerRule.java` 文件后，以下文件出现编译错误：

1. **CrawlerNetworkManager.java** - 缺少 `getRuleAntiSpider()` 方法
2. **CrawlerValidator.java** - 缺少各种 `getRule*()` 方法
3. **CrawlerStrategyFactory.java** - 类型转换错误
4. **AntiSpiderUtils.java** - 缺少 `getRuleAntiSpider()` 方法
5. **NovelCrawlerRuleServiceImpl.java** - 缺少各种规则对象方法
6. **ApiCrawlerStrategy.java** - 缺少规则获取方法
7. **HtmlCrawlerStrategy.java** - 缺少规则获取方法

## 恢复方案

### 1. 重新创建 NovelCrawlerRule 类

需要创建一个完整的 `NovelCrawlerRule` 类，包含以下内容：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    
    // 基本字段
    private String sourceName;
    private String sourceUrl;
    private String userAgent;
    private Map<String, String> headers;
    private Map<String, String> cookies;
    private Integer timeout;
    private CrawlerMode mode = CrawlerMode.HTML;
    
    // 规则ID字段
    private Long ruleSearchId;
    private Long ruleBookInfoId;
    private Long ruleChapterId;
    private Long ruleContentId;
    private Long ruleAntiSpiderId;
    
    // 关联对象（重要！）
    private RuleSearch ruleSearch;
    private RuleBookInfo ruleBookInfo;
    private RuleChapter ruleChapter;
    private RuleContent ruleContent;
    private RuleAntiSpider ruleAntiSpider;
    
    // 枚举
    public enum CrawlerMode {
        HTML, API
    }
    
    // 业务方法
    public boolean isComplete() { ... }
    public int getEffectiveTimeout() { ... }
    public boolean isHtmlMode() { ... }
    public boolean isApiMode() { ... }
    public String getDisplayName() { ... }
}
```

### 2. 关键点说明

#### 关联对象字段
最重要的是要包含关联对象字段，这些是其他类依赖的：
- `ruleSearch` - 搜索规则配置
- `ruleBookInfo` - 书籍信息规则配置
- `ruleChapter` - 章节规则配置
- `ruleContent` - 内容规则配置
- `ruleAntiSpider` - 反爬虫规则配置

#### 枚举类型
`CrawlerMode` 枚举必须包含，其他类有类型检查依赖。

#### 业务方法
包含常用的业务方法，提高代码可读性。

### 3. 文件创建步骤

由于文件系统可能存在缓存问题，按以下步骤操作：

1. **确认文件删除**：
   ```bash
   # 检查文件是否真的被删除
   ls shanhai-common/shanhai-common-crawler/src/main/java/com/shanhai/common/crawler/model/config/
   ```

2. **手动创建文件**：
   如果工具无法创建，可以手动创建文件并复制内容。

3. **验证编译**：
   创建后立即检查编译错误是否解决。

## 预防措施

### 1. 备份重要文件
在进行大规模重构前，应该备份关键文件：
```bash
cp NovelCrawlerRule.java NovelCrawlerRule.java.backup
```

### 2. 渐进式修改
- 不要一次性删除整个文件
- 先注释掉有问题的注解
- 逐步移除不需要的导入

### 3. 编译验证
每次修改后立即检查编译状态：
```bash
mvn compile -DskipTests
```

## 文件内容模板

以下是完整的 `NovelCrawlerRule.java` 文件内容模板：

```java
package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shanhai.common.core.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 小说爬虫规则主类
 * 
 * 作为所有规则配置的入口，聚合各子配置。
 * 支持配置校验和业务规则检查。
 * 
 * 注意：已移除 ACTable 注解以避免版本兼容性问题
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    
    // 基本配置字段
    @NotBlank(message = "站点名称不能为空")
    private String sourceName;
    
    @NotBlank(message = "站点URL不能为空")
    private String sourceUrl;
    
    private String userAgent;
    private Map<String, String> headers;
    private Map<String, String> cookies;
    private Integer timeout;
    
    @NotNull(message = "爬取模式不能为空")
    private CrawlerMode mode = CrawlerMode.HTML;
    
    // 规则ID字段
    private Long ruleSearchId;
    private Long ruleBookInfoId;
    private Long ruleChapterId;
    private Long ruleContentId;
    private Long ruleAntiSpiderId;
    
    // 关联对象字段（重要！）
    private RuleSearch ruleSearch;
    private RuleBookInfo ruleBookInfo;
    private RuleChapter ruleChapter;
    private RuleContent ruleContent;
    private RuleAntiSpider ruleAntiSpider;
    
    // 枚举定义
    public enum CrawlerMode {
        HTML, API
    }
    
    // 业务方法
    public boolean isComplete() {
        return sourceName != null && !sourceName.trim().isEmpty() &&
               sourceUrl != null && !sourceUrl.trim().isEmpty() &&
               mode != null;
    }
    
    public int getEffectiveTimeout() {
        return timeout != null && timeout > 0 ? timeout : 10000;
    }
    
    public boolean isHtmlMode() {
        return CrawlerMode.HTML.equals(mode);
    }
    
    public boolean isApiMode() {
        return CrawlerMode.API.equals(mode);
    }
    
    public String getDisplayName() {
        return sourceName != null ? sourceName : "未知站点";
    }
}
```

## 总结

这次事件提醒我们：
1. **重构需谨慎**：大规模修改前要做好备份
2. **渐进式修改**：避免一次性删除关键文件
3. **及时验证**：每次修改后立即检查编译状态
4. **依赖分析**：了解文件之间的依赖关系

通过重新创建完整的 `NovelCrawlerRule` 类，可以解决所有相关的编译错误。
