package com.shanhai.common.crawler.model.config;

import lombok.*;
import com.shanhai.common.core.model.BaseEntity;

/**
 * 章节列表页解析配置
 * <p>
 * 用于描述如何从章节目录页提取章节列表。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleChapter extends BaseEntity {
    /**
     * 章节项选择器
     */
    private String chapterItemSelector;
    /**
     * 章节名称选择器
     */
    private String chapterNameSelector;
    /**
     * 章节URL选择器
     */
    private String chapterUrlSelector;
    /**
     * 章节URL属性名
     */
    private String chapterUrlAttr;
    /**
     * 是否倒序（可选）
     */
    private Boolean reverseOrder;
    /**
     * 最大章节数（可选）
     */
    private Integer maxChapters;
    /**
     * 等待元素选择器（可选）
     */
    private String waitForSelector;
    /**
     * 关联主表ID
     */
    private String ruleId;
} 