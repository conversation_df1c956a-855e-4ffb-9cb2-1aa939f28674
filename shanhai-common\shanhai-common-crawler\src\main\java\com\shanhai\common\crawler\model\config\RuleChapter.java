package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.*;
import com.shanhai.common.core.model.BaseEntity;

/**
 * 章节列表页解析配置
 * <p>
 * 用于描述如何从章节目录页提取章节列表。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_rule_chapter")
@TableName("crawler_rule_chapter")
public class RuleChapter extends BaseEntity {
    /**
     * 章节项选择器
     */
    @Column(name = "chapter_item_selector", comment = "章节项选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String chapterItemSelector;

    /**
     * 章节名称选择器
     */
    @Column(name = "chapter_name_selector", comment = "章节名称选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String chapterNameSelector;

    /**
     * 章节URL选择器
     */
    @Column(name = "chapter_url_selector", comment = "章节URL选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String chapterUrlSelector;

    /**
     * 章节URL属性名
     */
    @Column(name = "chapter_url_attr", comment = "章节URL属性名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true, defaultValue = "href")
    private String chapterUrlAttr;

    /**
     * 是否倒序（可选）
     */
    @Column(name = "reverse_order", comment = "是否倒序", type = MySqlTypeConstant.TINYINT, length = 1, isNull = true, defaultValue = "0")
    private Boolean reverseOrder;

    /**
     * 最大章节数（可选）
     */
    @Column(name = "max_chapters", comment = "最大章节数", type = MySqlTypeConstant.INT, length = 11, isNull = true)
    private Integer maxChapters;

    /**
     * 等待元素选择器（可选）
     */
    @Column(name = "wait_for_selector", comment = "等待元素选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String waitForSelector;

    /**
     * 关联主表ID
     */
    @Column(name = "rule_id", comment = "关联主表ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleId;
} 