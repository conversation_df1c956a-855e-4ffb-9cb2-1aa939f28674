# ACTable BindingException 问题修复总结

## 问题描述

应用启动时出现以下错误：
```
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.gitee.sunchenbin.mybatis.actable.dao.system.CreateMysqlTablesMapper.findTableByTableName
```

这表明 MyBatis 无法找到 ACTable 内部的 Mapper XML 文件，通常是由于 MyBatis-Plus 和 ACTable 版本不兼容导致的。

## 问题分析

### 根本原因
1. **版本兼容性问题**：MyBatis-Plus 3.5.9 与 ACTable 1.5.0.RELEASE 存在兼容性问题
2. **Mapper 扫描冲突**：ACTable 的内部 Mapper 与 MyBatis-Plus 的扫描机制冲突
3. **XML 映射文件路径问题**：ACTable 的 XML 文件无法被正确加载

### 错误链路
```
StartUpHandlerImpl.startHandler() 
-> SysMysqlCreateTableManagerImpl.createMysqlTable()
-> CreateMysqlTablesMapper.findTableByTableName()
-> BindingException: Invalid bound statement
```

## 修复方案

### 方案1：禁用 ACTable，使用手动建表（推荐）

#### 1. 禁用 ACTable 自动建表
**文件**: `shanhai-api/src/main/resources/actable.properties`
```properties
# 暂时禁用自动建表，避免版本兼容性问题
mybatis.table.auto=none
```

#### 2. 移除 ACTable 相关配置
**启动类修改**:
```java
// 移除 ACTable 相关扫描
@MapperScan(basePackages = {
    "com.shanhai.common.crawler.repository",
    "com.shanhai.service.mapper"
})
@ComponentScan({
    "com.shanhai.api.controller",
    "com.shanhai.api.config",
    // ... 其他包，移除 ACTable 包
})
```

**MyBatis 配置修改**:
```yaml
mybatis-plus:
  # 移除 ACTable XML 路径
  mapper-locations: classpath*:mapper/**/*.xml
```

#### 3. 暂时注释 ACTable 依赖
**文件**: `shanhai-common/shanhai-common-crawler/pom.xml`
```xml
<!-- MyBatis ACTable - 暂时禁用，避免版本兼容性问题 -->
<!--
<dependency>
    <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
    <artifactId>mybatis-enhance-actable</artifactId>
</dependency>
-->
```

#### 4. 创建数据库初始化器
**文件**: `shanhai-api/src/main/java/com/shanhai/api/config/DatabaseInitializer.java`

```java
@Component
public class DatabaseInitializer implements CommandLineRunner {
    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        // 检查表是否存在，不存在则创建
        if (!tableExists(connection, "crawler_rule")) {
            createTables(connection);
        }
    }
}
```

#### 5. 简化实体类
移除所有 ACTable 注解，只保留 MyBatis-Plus 注解：

```java
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    @NotBlank(message = "站点名称不能为空")
    private String sourceName;
    
    @NotBlank(message = "站点URL不能为空")
    private String sourceUrl;
    
    // ... 其他字段，移除所有 @Column 注解
}
```

### 方案2：版本降级（备用方案）

如果必须使用 ACTable，可以尝试版本降级：

```xml
<!-- 降级到兼容版本 -->
<dependency>
    <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
    <artifactId>mybatis-enhance-actable</artifactId>
    <version>1.4.9.RELEASE</version>
</dependency>

<!-- 同时可能需要降级 MyBatis-Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.4.3</version>
</dependency>
```

## 修复后的文件结构

```
shanhai-api/src/main/
├── java/com/shanhai/api/config/
│   ├── DatabaseInitializer.java          # 新增：数据库初始化器
│   └── ACTableConfiguration.java         # 保留：配置加载类
├── resources/
│   ├── actable.properties                 # 修改：禁用自动建表
│   └── sql/
│       └── init_tables.sql               # 使用：手动建表脚本
```

## 验证修复

### 1. 检查应用启动
修复后，应用启动时应该：
- ✅ 不再出现 BindingException
- ✅ 能够正常启动到端口监听状态
- ✅ 显示启动成功信息

### 2. 检查数据库表
```sql
USE shanhai;
SHOW TABLES LIKE 'crawler_%';
-- 应该看到通过 DatabaseInitializer 创建的表
```

### 3. 检查日志输出
```
开始检查数据库表结构...
数据库表不存在，开始创建表结构...
数据库表创建完成
```

## 长期解决方案

### 1. 等待版本兼容性修复
- 关注 ACTable 项目更新
- 等待与 MyBatis-Plus 3.5.x 兼容的版本

### 2. 迁移到其他方案
- **Flyway**: 专业的数据库版本管理工具
- **Liquibase**: 企业级数据库变更管理
- **自定义脚本**: 基于 Spring Boot 的数据库初始化

### 3. 手动管理表结构
- 开发环境：使用提供的 SQL 脚本
- 生产环境：通过 DBA 管理表结构变更

## 最佳实践建议

### 1. 依赖管理
- **版本锁定**：在父 pom 中锁定关键依赖版本
- **兼容性测试**：升级前进行充分的兼容性测试
- **渐进式升级**：避免同时升级多个关键依赖

### 2. 数据库管理
- **版本控制**：将数据库脚本纳入版本控制
- **环境隔离**：不同环境使用不同的数据库管理策略
- **备份策略**：在结构变更前进行数据备份

### 3. 错误处理
- **优雅降级**：数据库初始化失败时不影响应用启动
- **详细日志**：记录数据库操作的详细信息
- **监控告警**：监控数据库连接和表结构状态

## 总结

通过禁用 ACTable 并使用手动建表的方式，成功解决了版本兼容性问题：

1. ✅ **移除冲突源**：禁用了有问题的 ACTable 自动建表功能
2. ✅ **提供替代方案**：创建了 DatabaseInitializer 来处理表创建
3. ✅ **简化依赖**：减少了复杂的版本兼容性问题
4. ✅ **保持功能**：应用功能不受影响，只是建表方式改变

这个解决方案是临时性的，长期来看建议迁移到更成熟的数据库版本管理工具。
