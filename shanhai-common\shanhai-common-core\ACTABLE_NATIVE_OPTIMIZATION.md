# ACTable 原生配置优化

## 优化内容

### 1. 配置文件优化

#### application-mybatis.yml
```yaml
# MyBatis ACTable 原生配置
mybatis:
  table:
    auto: update  # 自动更新表结构
  model:
    pack: com.shanhai.service.entity  # 实体类扫描包路径
```

### 2. BaseEntity 优化

优化了基础实体类的字段定义：

```java
@TableId(value = "id", type = IdType.ASSIGN_UUID)
@Column(name = "id", comment = "主键ID", type = MySqlTypeConstant.VARCHAR, length = 32, isKey = true, isNull = false)
private String id;

@Version
@TableField(fill = FieldFill.INSERT)
@Column(name = "version", comment = "乐观锁版本号", type = MySqlTypeConstant.INT, length = 11, isNull = false, defaultValue = "0")
private Integer version = 0;

@TableField(fill = FieldFill.INSERT)
@TableLogic(value = "0", delval = "1")
@Column(name = "deleted", comment = "逻辑删除标识(0:正常,1:删除)", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "0")
private Integer deleted = 0;
```

**主要改进**：
- 完善了字段注释，更加清晰明确
- 优化了字段类型和长度设置
- 添加了时区配置 `timezone = "GMT+8"`
- 统一了字段命名和约束设置

### 3. 实体类最佳实践

#### TestEntity 示例
```java
@Table(name = "test_entity")
public class TestEntity extends BaseEntity {
    
    @Column(name = "name", comment = "名称", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String name;

    @Column(name = "description", comment = "描述", type = MySqlTypeConstant.TEXT, isNull = true)
    private String description;

    @Column(name = "status", comment = "状态(0:禁用,1:启用)", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "1")
    private Integer status = 1;

    @Column(name = "sort_order", comment = "排序", type = MySqlTypeConstant.INT, length = 11, isNull = false, defaultValue = "0")
    private Integer sortOrder = 0;
    
    // Getters and Setters...
}
```

### 4. ACTable 注解使用规范

#### @Table 注解
```java
@Table(name = "table_name")  // 指定表名
```

#### @Column 注解
```java
@Column(
    name = "column_name",           // 列名
    comment = "列注释",             // 列注释
    type = MySqlTypeConstant.VARCHAR, // 数据类型
    length = 100,                   // 长度
    isNull = false,                 // 是否允许为空
    isKey = false,                  // 是否为主键
    isAutoIncrement = false,        // 是否自增
    defaultValue = "default_value"  // 默认值
)
```

### 5. 常用数据类型

```java
// 字符串类型
MySqlTypeConstant.VARCHAR    // 可变长字符串
MySqlTypeConstant.CHAR       // 定长字符串
MySqlTypeConstant.TEXT       // 长文本
MySqlTypeConstant.LONGTEXT   // 超长文本

// 数值类型
MySqlTypeConstant.TINYINT    // 小整数 (-128 到 127)
MySqlTypeConstant.INT        // 整数
MySqlTypeConstant.BIGINT     // 长整数
MySqlTypeConstant.DECIMAL    // 精确小数

// 日期时间类型
MySqlTypeConstant.DATE       // 日期
MySqlTypeConstant.TIME       // 时间
MySqlTypeConstant.DATETIME   // 日期时间
MySqlTypeConstant.TIMESTAMP  // 时间戳

// 其他类型
MySqlTypeConstant.BLOB       // 二进制大对象
MySqlTypeConstant.JSON       // JSON类型 (MySQL 5.7+)
```

### 6. 字段设计建议

#### 主键设计
```java
@TableId(value = "id", type = IdType.ASSIGN_UUID)
@Column(name = "id", comment = "主键ID", type = MySqlTypeConstant.VARCHAR, length = 32, isKey = true, isNull = false)
private String id;
```

#### 状态字段
```java
@Column(name = "status", comment = "状态(0:禁用,1:启用)", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "1")
private Integer status = 1;
```

#### 排序字段
```java
@Column(name = "sort_order", comment = "排序", type = MySqlTypeConstant.INT, length = 11, isNull = false, defaultValue = "0")
private Integer sortOrder = 0;
```

#### 时间字段
```java
@TableField(fill = FieldFill.INSERT)
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
@Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME, isNull = true)
private Date createTime;
```

### 7. 注意事项

1. **表名命名**：使用下划线分隔的小写字母，如 `user_info`
2. **字段命名**：使用下划线分隔的小写字母，如 `user_name`
3. **注释完整**：每个表和字段都应该有清晰的注释
4. **类型选择**：根据实际数据选择合适的类型和长度
5. **默认值**：为字段设置合理的默认值
6. **空值约束**：根据业务需求设置是否允许为空
7. **索引设计**：对于经常查询的字段考虑添加索引

### 8. 启动配置

确保在主启动类中正确配置了 ACTable 的扫描：

```java
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan(basePackages = {"com.gitee.sunchenbin.mybatis.actable.dao.*"})
@ComponentScan({
    "com.gitee.sunchenbin.mybatis.actable.manager.*",
    // 其他包扫描...
})
public class ShanHaiApplication {
    // ...
}
```

### 9. 依赖配置

确保 pom.xml 中包含正确的 ACTable 依赖：

```xml
<dependency>
    <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
    <artifactId>mybatis-enhance-actable</artifactId>
    <version>1.5.0.RELEASE</version>
    <exclusions>
        <exclusion>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

## 总结

通过以上优化，ACTable 的使用更加规范和高效：

1. **配置简化**：使用原生配置，简单明了
2. **注解规范**：统一的注解使用规范
3. **字段优化**：合理的字段类型和约束设置
4. **最佳实践**：提供了完整的使用示例

这样的配置既保持了 ACTable 原生的简洁性，又确保了表结构的规范性和可维护性。
