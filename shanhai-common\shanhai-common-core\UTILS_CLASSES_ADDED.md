# 工具类补充说明

## 新增工具类

为了解决编译错误，新增了以下两个工具类：

### 1. ValidateUtils - 验证工具类

**路径**: `com.shanhai.common.core.utils.ValidateUtils`

**主要功能**:
- 字符串验证（空值、空白、长度等）
- 集合和Map验证
- URL、邮箱、手机号、IP地址格式验证
- 数字范围验证
- 正则表达式匹配
- 爬虫相关验证（CSS选择器、XPath、JSON Path等）
- 超时时间和延迟时间验证
- User-Agent验证

**常用方法**:
```java
// 基础验证
ValidateUtils.isEmpty(str)
ValidateUtils.isNotEmpty(str)
ValidateUtils.isBlank(str)
ValidateUtils.isNotBlank(str)

// 格式验证
ValidateUtils.isValidUrl(url)
ValidateUtils.isValidEmail(email)
ValidateUtils.isValidPhone(phone)

// 爬虫相关验证
ValidateUtils.isValidCssSelector(selector)
ValidateUtils.isValidXPath(xpath)
ValidateUtils.isValidTimeout(timeout)
```

### 2. JsonUtils - JSON工具类

**路径**: `com.shanhai.common.core.utils.JsonUtils`

**主要功能**:
- 对象与JSON字符串互转
- 支持泛型类型转换
- List和Map的JSON转换
- JSON有效性验证
- 对象深拷贝
- JSON合并
- 字段值提取

**常用方法**:
```java
// 基础转换
JsonUtils.toJson(object)
JsonUtils.fromJson(json, Class.class)
JsonUtils.fromJsonToList(json, Class.class)
JsonUtils.fromJsonToMap(json)

// 实用功能
JsonUtils.isValidJson(json)
JsonUtils.deepCopy(object, Class.class)
JsonUtils.mergeJson(json1, json2)
JsonUtils.getFieldValue(json, fieldName)

// 爬虫配置相关
JsonUtils.listToJsonArray(list)
JsonUtils.jsonArrayToList(jsonArray)
JsonUtils.mapToJson(map)
JsonUtils.jsonToStringMap(json)
```

## 依赖关系

这两个工具类主要依赖：
- **Hutool**: 提供基础的字符串、集合操作
- **Jackson**: 提供JSON序列化/反序列化功能
- **Lombok**: 提供日志注解支持

### Jackson 依赖说明
- **必需**: `jackson-databind` (已包含在项目中)
- **可选**: `jackson-datatype-jsr310` (支持Java 8时间类型)
  - 如果存在此依赖，JsonUtils会自动启用Java 8时间类型支持
  - 如果不存在，会跳过时间类型支持，不影响其他功能

## 使用场景

### ValidateUtils 使用场景
- 爬虫配置验证（CrawlerValidator中使用）
- 用户输入参数验证
- 数据格式校验
- 业务规则验证

### JsonUtils 使用场景
- 爬虫规则管理（RuleManager中使用）
- 配置文件处理
- API数据转换
- 对象序列化存储

## 注意事项

1. **线程安全**: 两个工具类都是线程安全的
2. **异常处理**: JsonUtils中的异常已经被捕获并记录日志
3. **性能考虑**: ObjectMapper实例被复用，避免重复创建
4. **扩展性**: 可以根据业务需要添加更多验证和转换方法

## 编译问题解决

### 原编译错误：
```
java: 找不到符号
  符号:   类 ValidateUtils
  位置: 程序包 com.shanhai.common.core.utils

java: 找不到符号
  符号:   类 JsonUtils
  位置: 程序包 com.shanhai.common.core.utils

java: 程序包com.fasterxml.jackson.datatype.jsr310不存在

java: 找不到符号
  符号:   方法 of()
  位置: 接口 java.util.List

java: 找不到符号
  符号:   方法 isUrl(java.lang.String)
  位置: 类 com.shanhai.common.core.utils.ValidateUtils

java: 找不到符号
  符号:   方法 readAllBytes()
  位置: 类型为java.io.InputStream的变量 inputStream
```

### 解决方案：
1. ✅ 创建了 `ValidateUtils` 工具类
2. ✅ 创建了 `JsonUtils` 工具类
3. ✅ 修复了 Jackson JSR310 依赖问题
   - 使用反射动态加载 JavaTimeModule
   - 如果依赖存在则启用Java 8时间支持
   - 如果依赖不存在则跳过，不影响其他功能
4. ✅ 修复了 Java 版本兼容性问题
   - 将 `List.of()` (Java 9+) 替换为 `Collections.emptyList()` (Java 8+)
   - 添加泛型类型声明确保类型安全
   - 保持代码在 Java 8 环境下的兼容性
5. ✅ 补充了缺失的工具方法
   - 在 `ValidateUtils` 中添加了 `isUrl()` 方法（`isValidUrl()` 的别名）
   - 确保向后兼容性，支持旧代码调用
6. ✅ 修复了 InputStream 兼容性问题
   - 将 `InputStream.readAllBytes()` (Java 9+) 替换为自定义实现
   - 使用 `ByteArrayOutputStream` 实现 Java 8 兼容的字节读取
   - 保持相同的功能和性能

现已完全解决所有编译错误。
