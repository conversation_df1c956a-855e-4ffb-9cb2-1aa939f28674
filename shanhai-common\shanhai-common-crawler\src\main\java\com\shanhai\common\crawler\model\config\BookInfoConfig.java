package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookInfoConfig {
    private String nameSelector;
    private String authorSelector;
    private String coverSelector;
    private String introSelector;
    private String categorySelector;
    private String wordCountSelector;
    private String lastChapterSelector;
    private String statusSelector;
    private String updateTimeSelector;
    private String chapterListUrlSelector;
    private String chapterListUrlAttr;
    private String waitForSelector;
} 