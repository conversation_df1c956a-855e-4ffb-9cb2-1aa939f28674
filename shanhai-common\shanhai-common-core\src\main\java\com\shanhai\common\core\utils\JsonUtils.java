package com.shanhai.common.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        // 配置ObjectMapper
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
    }

    /**
     * 对象转JSON字符串
     * 
     * @param obj 对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 对象转格式化的JSON字符串
     * 
     * @param obj 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转格式化JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (ValidateUtils.isBlank(json) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象（支持泛型）
     * 
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (ValidateUtils.isBlank(json) || typeReference == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转List
     * 
     * @param json JSON字符串
     * @param clazz 列表元素类型
     * @param <T> 泛型类型
     * @return List对象
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> clazz) {
        if (ValidateUtils.isBlank(json) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, 
                OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            log.error("JSON转List失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转Map
     * 
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> fromJsonToMap(String json) {
        if (ValidateUtils.isBlank(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转指定类型的Map
     * 
     * @param json JSON字符串
     * @param keyClass Key类型
     * @param valueClass Value类型
     * @param <K> Key泛型类型
     * @param <V> Value泛型类型
     * @return Map对象
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (ValidateUtils.isBlank(json) || keyClass == null || valueClass == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, 
                OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (JsonProcessingException e) {
            log.error("JSON转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查字符串是否为有效的JSON
     * 
     * @param json JSON字符串
     * @return 是否为有效JSON
     */
    public static boolean isValidJson(String json) {
        if (ValidateUtils.isBlank(json)) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 深拷贝对象（通过JSON序列化和反序列化）
     * 
     * @param obj 源对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 拷贝后的对象
     */
    public static <T> T deepCopy(Object obj, Class<T> clazz) {
        if (obj == null || clazz == null) {
            return null;
        }
        try {
            String json = OBJECT_MAPPER.writeValueAsString(obj);
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("深拷贝失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 合并两个JSON对象
     * 
     * @param json1 第一个JSON字符串
     * @param json2 第二个JSON字符串
     * @return 合并后的JSON字符串
     */
    public static String mergeJson(String json1, String json2) {
        if (ValidateUtils.isBlank(json1)) {
            return json2;
        }
        if (ValidateUtils.isBlank(json2)) {
            return json1;
        }
        
        try {
            Map<String, Object> map1 = fromJsonToMap(json1);
            Map<String, Object> map2 = fromJsonToMap(json2);
            
            if (map1 == null) {
                return json2;
            }
            if (map2 == null) {
                return json1;
            }
            
            map1.putAll(map2);
            return toJson(map1);
        } catch (Exception e) {
            log.error("合并JSON失败: {}", e.getMessage(), e);
            return json1;
        }
    }

    /**
     * 从JSON中提取指定字段的值
     * 
     * @param json JSON字符串
     * @param fieldName 字段名
     * @return 字段值
     */
    public static Object getFieldValue(String json, String fieldName) {
        if (ValidateUtils.isBlank(json) || ValidateUtils.isBlank(fieldName)) {
            return null;
        }
        
        Map<String, Object> map = fromJsonToMap(json);
        if (map == null) {
            return null;
        }
        
        return map.get(fieldName);
    }

    /**
     * 从JSON中提取指定字段的字符串值
     * 
     * @param json JSON字符串
     * @param fieldName 字段名
     * @return 字段的字符串值
     */
    public static String getFieldStringValue(String json, String fieldName) {
        Object value = getFieldValue(json, fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * 字符串列表转JSON数组字符串
     * 
     * @param list 字符串列表
     * @return JSON数组字符串
     */
    public static String listToJsonArray(List<String> list) {
        if (ValidateUtils.isEmpty(list)) {
            return "[]";
        }
        return toJson(list);
    }

    /**
     * JSON数组字符串转字符串列表
     * 
     * @param jsonArray JSON数组字符串
     * @return 字符串列表
     */
    public static List<String> jsonArrayToList(String jsonArray) {
        if (ValidateUtils.isBlank(jsonArray)) {
            return null;
        }
        return fromJsonToList(jsonArray, String.class);
    }

    /**
     * Map转JSON字符串
     * 
     * @param map Map对象
     * @return JSON字符串
     */
    public static String mapToJson(Map<String, String> map) {
        if (ValidateUtils.isEmpty(map)) {
            return "{}";
        }
        return toJson(map);
    }

    /**
     * JSON字符串转String Map
     * 
     * @param json JSON字符串
     * @return String Map
     */
    public static Map<String, String> jsonToStringMap(String json) {
        if (ValidateUtils.isBlank(json)) {
            return null;
        }
        return fromJsonToMap(json, String.class, String.class);
    }
}
