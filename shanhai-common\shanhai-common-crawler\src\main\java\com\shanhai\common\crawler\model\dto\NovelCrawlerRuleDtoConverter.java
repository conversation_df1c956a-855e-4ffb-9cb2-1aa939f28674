package com.shanhai.common.crawler.model.dto;

import com.shanhai.common.crawler.model.NovelCrawlerRule;
import com.shanhai.common.crawler.model.config.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * NovelCrawlerRuleDto与实体类的转换工具
 * <p>
 * 提供DTO与实体的相互转换方法，支持嵌套结构递归转换。
 *
 * <AUTHOR>
 */
public class NovelCrawlerRuleDtoConverter {
    public static NovelCrawlerRule toEntity(NovelCrawlerRuleDto dto) {
        if (dto == null) return null;
        return NovelCrawlerRule.builder()
                .ruleName(dto.getRuleName())
                .sourceInfo(toEntity(dto.getSourceInfo()))
                .mode(dto.getMode())
                .searchConfig(toEntity(dto.getSearchConfig()))
                .bookInfoConfig(toEntity(dto.getBookInfoConfig()))
                .chapterListConfig(toEntity(dto.getChapterListConfig()))
                .chapterContentConfig(toEntity(dto.getChapterContentConfig()))
                .antiSpiderConfig(toEntity(dto.getAntiSpiderConfig()))
                .build();
    }
    public static SourceInfo toEntity(NovelCrawlerRuleDto.SourceInfo dto) {
        if (dto == null) return null;
        return SourceInfo.builder()
                .sourceName(dto.getSourceName())
                .sourceUrl(dto.getSourceUrl())
                .userAgent(dto.getUserAgent())
                .headers(dto.getHeaders())
                .cookies(dto.getCookies())
                .timeout(dto.getTimeout())
                .build();
    }
    public static SearchConfig toEntity(NovelCrawlerRuleDto.SearchConfig dto) {
        if (dto == null) return null;
        return SearchConfig.builder()
                .searchUrl(dto.getSearchUrl())
                .searchParam(dto.getSearchParam())
                .bookListSelector(dto.getBookListSelector())
                .bookItemSelector(dto.getBookItemSelector())
                .bookItem(toEntity(dto.getBookItem()))
                .waitForSelector(dto.getWaitForSelector())
                .build();
    }
    public static BookItemConfig toEntity(NovelCrawlerRuleDto.BookItemConfig dto) {
        if (dto == null) return null;
        return BookItemConfig.builder()
                .nameSelector(dto.getNameSelector())
                .authorSelector(dto.getAuthorSelector())
                .coverSelector(dto.getCoverSelector())
                .introSelector(dto.getIntroSelector())
                .categorySelector(dto.getCategorySelector())
                .wordCountSelector(dto.getWordCountSelector())
                .lastChapterSelector(dto.getLastChapterSelector())
                .bookUrlSelector(dto.getBookUrlSelector())
                .bookUrlAttr(dto.getBookUrlAttr())
                .build();
    }
    public static BookInfoConfig toEntity(NovelCrawlerRuleDto.BookInfoConfig dto) {
        if (dto == null) return null;
        return BookInfoConfig.builder()
                .nameSelector(dto.getNameSelector())
                .authorSelector(dto.getAuthorSelector())
                .coverSelector(dto.getCoverSelector())
                .introSelector(dto.getIntroSelector())
                .categorySelector(dto.getCategorySelector())
                .wordCountSelector(dto.getWordCountSelector())
                .lastChapterSelector(dto.getLastChapterSelector())
                .statusSelector(dto.getStatusSelector())
                .updateTimeSelector(dto.getUpdateTimeSelector())
                .chapterListUrlSelector(dto.getChapterListUrlSelector())
                .chapterListUrlAttr(dto.getChapterListUrlAttr())
                .waitForSelector(dto.getWaitForSelector())
                .build();
    }
    public static ChapterListConfig toEntity(NovelCrawlerRuleDto.ChapterListConfig dto) {
        if (dto == null) return null;
        return ChapterListConfig.builder()
                .chapterItemSelector(dto.getChapterItemSelector())
                .chapterNameSelector(dto.getChapterNameSelector())
                .chapterUrlSelector(dto.getChapterUrlSelector())
                .chapterUrlAttr(dto.getChapterUrlAttr())
                .reverseOrder(dto.getReverseOrder())
                .maxChapters(dto.getMaxChapters())
                .waitForSelector(dto.getWaitForSelector())
                .build();
    }
    public static ChapterContentConfig toEntity(NovelCrawlerRuleDto.ChapterContentConfig dto) {
        if (dto == null) return null;
        return ChapterContentConfig.builder()
                .titleSelector(dto.getTitleSelector())
                .contentSelector(dto.getContentSelector())
                .contentFilter(dto.getContentFilter())
                .replaceRules(toReplaceRuleList(dto.getReplaceRules()))
                .removeHtml(dto.getRemoveHtml())
                .removeSelectors(dto.getRemoveSelectors())
                .nextChapterSelector(dto.getNextChapterSelector())
                .nextChapterAttr(dto.getNextChapterAttr())
                .prevChapterSelector(dto.getPrevChapterSelector())
                .prevChapterAttr(dto.getPrevChapterAttr())
                .waitForSelector(dto.getWaitForSelector())
                .build();
    }
    public static AntiSpiderConfig toEntity(NovelCrawlerRuleDto.AntiSpiderConfig dto) {
        if (dto == null) return null;
        return AntiSpiderConfig.builder()
                .userAgents(dto.getUserAgents())
                .proxyList(dto.getProxyList())
                .minDelayMs(dto.getMinDelayMs())
                .maxDelayMs(dto.getMaxDelayMs())
                .build();
    }
    public static ReplaceRule toEntity(NovelCrawlerRuleDto.ReplaceRule dto) {
        if (dto == null) return null;
        return ReplaceRule.builder()
                .pattern(dto.getPattern())
                .replacement(dto.getReplacement())
                .build();
    }
    public static List<ReplaceRule> toReplaceRuleList(List<NovelCrawlerRuleDto.ReplaceRule> list) {
        if (list == null) return null;
        return list.stream().map(NovelCrawlerRuleDtoConverter::toEntity).collect(Collectors.toList());
    }
} 