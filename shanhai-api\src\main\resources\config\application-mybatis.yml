# MyBatis-Plus 配置
mybatis-plus:
  # 配置实体类包路径
  type-aliases-package: com.shanhai.**.model
  # 配置mapper.xml文件路径，补全 Actable 的 XML 路径
  mapper-locations: classpath*:mapper/**/*.xml,classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*Mapper.xml
  # 全局配置
  global-config:
    # 是否打印SQL日志
    banner: false
    db-config:
      # 主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID" ASSIGN_ID:"全局唯一ID (数字类型)" UUID:"全局唯一ID UUID"
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 字段策略 IGNORED:"忽略判断" NOT_NULL:"非NULL判断" NOT_EMPTY:"非空判断"
      insert-strategy: NOT_NULL
      update-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  configuration:
    # 开启缓存
    cache-enabled: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 设置积极的延迟加载
    aggressive-lazy-loading: false
    # 开启自动生成主键
    use-generated-keys: true
    # 开启结果集自动映射
    auto-mapping-behavior: PARTIAL
    # 开启自动映射未知列
    auto-mapping-unknown-column-behavior: WARNING
    # 设置默认执行器
    default-executor-type: SIMPLE
    # 设置默认语句超时时间
    default-statement-timeout: 30
    # 设置默认获取结果集超时时间
    default-fetch-size: 100
    # 设置默认结果集类型
    default-result-set-type: FORWARD_ONLY
    # 是否允许在嵌套语句中使用分页
    safe-row-bounds-enabled: false
    # 是否允许使用自定义的主键值
    safe-result-handler-enabled: true
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 是否开启本地缓存机制
    local-cache-scope: SESSION
    # 设置但JDBC类型为空时,某些驱动程序要指定值
    jdbc-type-for-null: OTHER
    # 设置触发延迟加载的方法
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    # 设置日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 分页插件配置
pagehelper:
  # 分页插件会自动检测当前的数据库链接，自动选择合适的分页方式
  helper-dialect: mysql
  # 分页合理化参数，默认值为false
  reasonable: true
  # 支持通过Mapper接口参数来传递分页参数
  support-methods-arguments: true
  # 分页参数合理化
  params: count=countSql
  # 默认值为 false，该参数对使用 RowBounds 作为分页参数时有效
  row-bounds-with-count: true
  # 分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到对应的值时，自动分页
  page-size-zero: true

# application.yml 或 application-mybatis.yml
mybatis:
  table:
    auto: update
  model:
    pack: com.shanhai.service.entity

# 动态表创建配置
mybatis-enhance:
  # 是否开启自动建表
  auto-create-table: true
  # 是否开启自动更新表结构
  auto-update-table: true
  # 是否开启自动删除表
  auto-drop-table: false
  # 表前缀
  table-prefix: 
  # 表后缀
  table-suffix: 
  # 是否开启驼峰命名
  camel-case: true
  # 是否开启下划线命名
  underscore: true
  # 是否开启注释
  comment: true
  # 是否开启索引
  index: true
  # 是否开启外键
  foreign-key: true
  # 是否开启唯一约束
  unique: true
  # 是否开启非空约束
  not-null: true
  # 是否开启默认值
  default-value: true
  # 是否开启自增
  auto-increment: true
  # 是否开启主键
  primary-key: true
  # 是否开启外键约束
  foreign-key-constraint: true
  # 是否开启唯一约束
  unique-constraint: true
  # 是否开启非空约束
  not-null-constraint: true
  # 是否开启默认值约束
  default-value-constraint: true
  # 是否开启自增约束
  auto-increment-constraint: true
  # 是否开启主键约束
  primary-key-constraint: true