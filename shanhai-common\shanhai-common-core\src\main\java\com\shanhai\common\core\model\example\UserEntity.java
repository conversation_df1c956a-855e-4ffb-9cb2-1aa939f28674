package com.shanhai.common.core.model.example;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.annotation.ActableEnhanced;
import com.shanhai.common.core.annotation.ColumnEnhanced;
import com.shanhai.common.core.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类示例
 * <p>
 * 展示如何使用 ACTable 注解和增强注解
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "sys_user")
@TableName("sys_user")
@ActableEnhanced(
    comment = "系统用户表",
    engine = "InnoDB",
    charset = "utf8mb4",
    collate = "utf8mb4_unicode_ci",
    enableAudit = true,
    enableSoftDelete = true,
    enableOptimisticLock = true,
    indexes = {
        @ActableEnhanced.IndexDef(
            name = "idx_username",
            columns = {"username"},
            type = ActableEnhanced.IndexDef.IndexType.UNIQUE,
            comment = "用户名唯一索引"
        ),
        @ActableEnhanced.IndexDef(
            name = "idx_email",
            columns = {"email"},
            type = ActableEnhanced.IndexDef.IndexType.NORMAL,
            comment = "邮箱索引"
        ),
        @ActableEnhanced.IndexDef(
            name = "idx_phone",
            columns = {"phone"},
            type = ActableEnhanced.IndexDef.IndexType.NORMAL,
            comment = "手机号索引"
        ),
        @ActableEnhanced.IndexDef(
            name = "idx_status_create_time",
            columns = {"status", "create_time"},
            type = ActableEnhanced.IndexDef.IndexType.NORMAL,
            comment = "状态和创建时间复合索引"
        )
    },
    uniques = {
        @ActableEnhanced.UniqueDef(
            name = "uk_username",
            columns = {"username"},
            comment = "用户名唯一约束"
        )
    }
)
public class UserEntity extends BaseEntity {

    @Column(name = "username", comment = "用户名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = false)
    @ColumnEnhanced(
        label = "用户名",
        businessMeaning = "系统登录用户名，全局唯一",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 1,
        group = "基本信息",
        minLength = 3,
        maxLength = 50,
        regex = "^[a-zA-Z0-9_]{3,50}$",
        placeholder = "请输入用户名",
        helpText = "用户名只能包含字母、数字和下划线，长度3-50位"
    )
    private String username;

    @Column(name = "password", comment = "密码", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    @ColumnEnhanced(
        label = "密码",
        businessMeaning = "用户登录密码，加密存储",
        searchable = false,
        sortable = false,
        listVisible = false,
        detailVisible = false,
        formVisible = true,
        displayOrder = 2,
        group = "基本信息",
        sensitive = true,
        sensitiveType = ColumnEnhanced.SensitiveType.PASSWORD,
        minLength = 6,
        maxLength = 20,
        placeholder = "请输入密码",
        helpText = "密码长度6-20位，建议包含字母、数字和特殊字符"
    )
    private String password;

    @Column(name = "real_name", comment = "真实姓名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true)
    @ColumnEnhanced(
        label = "真实姓名",
        businessMeaning = "用户真实姓名",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 3,
        group = "基本信息",
        sensitive = true,
        sensitiveType = ColumnEnhanced.SensitiveType.NAME,
        maxLength = 50,
        placeholder = "请输入真实姓名"
    )
    private String realName;

    @Column(name = "email", comment = "邮箱", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = true)
    @ColumnEnhanced(
        label = "邮箱",
        businessMeaning = "用户邮箱地址，用于找回密码等功能",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 4,
        group = "联系信息",
        sensitive = true,
        sensitiveType = ColumnEnhanced.SensitiveType.EMAIL,
        maxLength = 100,
        regex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
        placeholder = "请输入邮箱地址",
        helpText = "请输入有效的邮箱地址"
    )
    private String email;

    @Column(name = "phone", comment = "手机号", type = MySqlTypeConstant.VARCHAR, length = 20, isNull = true)
    @ColumnEnhanced(
        label = "手机号",
        businessMeaning = "用户手机号码",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 5,
        group = "联系信息",
        sensitive = true,
        sensitiveType = ColumnEnhanced.SensitiveType.PHONE,
        maxLength = 20,
        regex = "^1[3-9]\\d{9}$",
        placeholder = "请输入手机号",
        helpText = "请输入有效的手机号码"
    )
    private String phone;

    @Column(name = "avatar", comment = "头像", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    @ColumnEnhanced(
        label = "头像",
        businessMeaning = "用户头像图片URL",
        searchable = false,
        sortable = false,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 6,
        group = "基本信息",
        maxLength = 200,
        placeholder = "请选择头像"
    )
    private String avatar;

    @Column(name = "status", comment = "状态", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "1")
    @ColumnEnhanced(
        label = "状态",
        businessMeaning = "用户账号状态：1-正常，0-禁用",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 7,
        group = "状态信息",
        dictType = "user_status",
        defaultValueExpression = "1"
    )
    private Integer status = 1;

    @Column(name = "gender", comment = "性别", type = MySqlTypeConstant.TINYINT, length = 1, isNull = true, defaultValue = "0")
    @ColumnEnhanced(
        label = "性别",
        businessMeaning = "用户性别：0-未知，1-男，2-女",
        searchable = true,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = true,
        displayOrder = 8,
        group = "基本信息",
        dictType = "gender",
        defaultValueExpression = "0"
    )
    private Integer gender = 0;

    @Column(name = "birthday", comment = "生日", type = MySqlTypeConstant.DATE, isNull = true)
    @ColumnEnhanced(
        label = "生日",
        businessMeaning = "用户生日",
        searchable = false,
        sortable = true,
        listVisible = false,
        detailVisible = true,
        formVisible = true,
        displayOrder = 9,
        group = "基本信息",
        format = "yyyy-MM-dd",
        placeholder = "请选择生日"
    )
    private java.util.Date birthday;

    @Column(name = "last_login_time", comment = "最后登录时间", type = MySqlTypeConstant.DATETIME, isNull = true)
    @ColumnEnhanced(
        label = "最后登录时间",
        businessMeaning = "用户最后一次登录系统的时间",
        searchable = false,
        sortable = true,
        listVisible = true,
        detailVisible = true,
        formVisible = false,
        displayOrder = 10,
        group = "状态信息",
        format = "yyyy-MM-dd HH:mm:ss"
    )
    private java.util.Date lastLoginTime;

    @Column(name = "login_count", comment = "登录次数", type = MySqlTypeConstant.BIGINT, length = 20, isNull = false, defaultValue = "0")
    @ColumnEnhanced(
        label = "登录次数",
        businessMeaning = "用户累计登录次数",
        searchable = false,
        sortable = true,
        listVisible = false,
        detailVisible = true,
        formVisible = false,
        displayOrder = 11,
        group = "统计信息",
        minValue = 0,
        defaultValueExpression = "0"
    )
    private Long loginCount = 0L;
}
