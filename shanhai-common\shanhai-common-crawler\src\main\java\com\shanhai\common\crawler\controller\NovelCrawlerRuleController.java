package com.shanhai.common.crawler.controller;

import com.shanhai.common.crawler.model.NovelCrawlerRule;
import com.shanhai.common.crawler.service.NovelCrawlerRuleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import javax.validation.Valid;

/**
 * 小说爬虫规则管理控制器
 * <p>
 * 提供规则的增删改查REST接口。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/crawler/rule")
public class NovelCrawlerRuleController {
    @Resource
    private NovelCrawlerRuleService ruleService;

    /**
     * 查询所有爬虫规则
     *
     * @return 规则列表
     */
    @GetMapping
    public List<NovelCrawlerRule> list() {
        return ruleService.list();
    }

    /**
     * 根据ID查询单个爬虫规则
     *
     * @param id 规则ID
     * @return 规则详情
     */
    @GetMapping("/{id}")
    public NovelCrawlerRule get(@PathVariable Long id) {
        return ruleService.getById(id);
    }

    /**
     * 新增爬虫规则
     *
     * @param rule 规则对象
     * @return 是否成功
     */
    @PostMapping
    public boolean add(@Valid @RequestBody NovelCrawlerRule rule) {
        return ruleService.save(rule);
    }

    /**
     * 更新爬虫规则
     *
     * @param rule 规则对象
     * @return 是否成功
     */
    @PutMapping
    public boolean update(@Valid @RequestBody NovelCrawlerRule rule) {
        return ruleService.updateById(rule);
    }

    /**
     * 删除爬虫规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Long id) {
        return ruleService.removeById(id);
    }
} 