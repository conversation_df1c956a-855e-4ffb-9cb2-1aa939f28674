package com.shanhai.common.crawler.strategy;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import com.shanhai.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 爬虫策略工厂
 * <p>
 * 根据规则类型动态获取对应的爬虫策略实现。
 * 支持扩展多种采集模式（如API、HTML、Selenium等），实现策略解耦。
 * 线程安全：无状态工厂，线程安全。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlerStrategyFactory {

    private static final Map<String, CrawlerStrategy> STRATEGY_CACHE = new ConcurrentHashMap<>();

    // 支持的采集模式
    public static final String MODE_HTML = "HTML";
    public static final String MODE_API = "API";
    public static final String MODE_SELENIUM = "SELENIUM";
    public static final String MODE_AUTO = "AUTO";

    static {
        // 初始化策略实例
        STRATEGY_CACHE.put(MODE_HTML, new HtmlCrawlerStrategy());
        STRATEGY_CACHE.put(MODE_API, new ApiCrawlerStrategy());
        // 可以继续添加其他策略
        // STRATEGY_CACHE.put(MODE_SELENIUM, new SeleniumCrawlerStrategy());
    }

    /**
     * 根据规则获取对应的爬虫策略实现
     *
     * @param rule 爬虫规则配置，包含采集模式等关键信息
     * @return 爬虫策略实现
     * @throws CrawlerException 当策略不存在时抛出异常
     */
    public static CrawlerStrategy getStrategy(NovelCrawlerRule rule) {
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }

        String mode = rule.getMode();
        if (StringUtils.isBlank(mode)) {
            log.warn("采集模式为空，使用默认HTML模式");
            mode = MODE_HTML;
        }

        mode = mode.toUpperCase();

        // 自动模式：根据规则特征自动选择策略
        if (MODE_AUTO.equals(mode)) {
            mode = autoDetectMode(rule);
        }

        CrawlerStrategy strategy = STRATEGY_CACHE.get(mode);
        if (strategy == null) {
            throw new CrawlerException(CrawlerErrorCode.STRATEGY_NOT_FOUND,
                "不支持的采集模式: " + mode + "，支持的模式: " + getSupportedModes());
        }

        log.debug("使用采集策略: {} for {}", mode, rule.getSourceName());
        return strategy;
    }

    /**
     * 自动检测采集模式
     */
    private static String autoDetectMode(NovelCrawlerRule rule) {
        // 根据URL特征判断
        if (rule.getRuleSearch() != null && rule.getRuleSearch().getUrl() != null) {
            String searchUrl = rule.getRuleSearch().getUrl();

            // 如果搜索URL包含API相关关键词，使用API模式
            if (searchUrl.contains("/api/") || searchUrl.contains("json") || searchUrl.contains("ajax")) {
                return MODE_API;
            }
        }

        // 默认使用HTML模式
        return MODE_HTML;
    }

    /**
     * 获取支持的采集模式列表
     */
    public static String getSupportedModes() {
        return String.join(", ", STRATEGY_CACHE.keySet());
    }

    /**
     * 检查是否支持指定模式
     */
    public static boolean isSupported(String mode) {
        return StringUtils.isNotBlank(mode) && STRATEGY_CACHE.containsKey(mode.toUpperCase());
    }

    /**
     * 注册新的策略
     */
    public static void registerStrategy(String mode, CrawlerStrategy strategy) {
        if (StringUtils.isNotBlank(mode) && strategy != null) {
            STRATEGY_CACHE.put(mode.toUpperCase(), strategy);
            log.info("注册新的爬虫策略: {}", mode);
        }
    }

    /**
     * 获取所有已注册的策略
     */
    public static Map<String, CrawlerStrategy> getAllStrategies() {
        return new ConcurrentHashMap<>(STRATEGY_CACHE);
    }
}