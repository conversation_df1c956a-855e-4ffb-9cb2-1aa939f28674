package com.shanhai.common.crawler.strategy;


import com.shanhai.common.crawler.model.NovelCrawlerRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 爬虫策略工厂
 * <p>
 * 根据配置选择合适的采集策略（API、Selenium、HTML）。
 * 内部包含三种策略的实现类，所有依赖通过构造器注入，便于测试和维护。
 * <p>
 * 策略优先级：
 * 1. API策略 - 优先使用，效率最高
 * 2. Selenium策略 - 动态网站，支持JavaScript渲染
 * 3. HTML策略 - 静态网站，性能较好
 * <p>
 * 线程安全：无状态工厂，线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class CrawlerStrategyFactory {

    private final ApiCrawlerStrategy apiCrawlerStrategy = new ApiCrawlerStrategy();
    private final HtmlCrawlerStrategy htmlCrawlerStrategy = new HtmlCrawlerStrategy();

    /**
     * 根据规则配置选择策略
     */
    public CrawlerStrategy getStrategy(NovelCrawlerRule config) {
        if (config == null || config.getMode() == null) {
            return htmlCrawlerStrategy;
        }
        switch (config.getMode().toUpperCase()) {
            case "API":
                return apiCrawlerStrategy;
            case "HTML":
            default:
                return htmlCrawlerStrategy;
        }
    }
} 