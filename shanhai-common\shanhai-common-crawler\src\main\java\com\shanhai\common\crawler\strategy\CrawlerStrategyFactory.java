package com.shanhai.common.crawler.strategy;

import com.shanhai.common.crawler.model.config.NovelCrawlerRule;

/**
 * 爬虫策略工厂
 * <p>
 * 根据规则类型动态获取对应的爬虫策略实现。
 * 支持扩展多种采集模式（如API、HTML、Selenium等），实现策略解耦。
 * 线程安全：无状态工厂，线程安全。
 *
 * <AUTHOR>
 */
public class CrawlerStrategyFactory {

    /**
     * 根据规则获取对应的爬虫策略实现
     *
     * @param rule 爬虫规则配置，包含采集模式等关键信息
     * @return 爬虫策略实现，默认返回 HtmlCrawlerStrategy，可扩展支持多种模式
     */
    public static CrawlerStrategy getStrategy(NovelCrawlerRule rule) {
        // 示例：根据 rule 类型返回不同策略，实际可扩展
        // return new ApiCrawlerStrategy();
        return new HtmlCrawlerStrategy();
    }
} 