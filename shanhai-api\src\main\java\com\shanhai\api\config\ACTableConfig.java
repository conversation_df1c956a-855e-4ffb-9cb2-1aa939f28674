package com.shanhai.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * ACTable 配置类
 * 
 * 用于配置 MyBatis ACTable 自动建表功能
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "mybatis")
public class ACTableConfig {
    
    /**
     * 表配置
     */
    private Table table = new Table();
    
    /**
     * 模型配置
     */
    private Model model = new Model();
    
    public Table getTable() {
        return table;
    }
    
    public void setTable(Table table) {
        this.table = table;
    }
    
    public Model getModel() {
        return model;
    }
    
    public void setModel(Model model) {
        this.model = model;
    }
    
    /**
     * 表配置内部类
     */
    public static class Table {
        /**
         * 自动建表模式
         * none: 不执行
         * create: 每次启动都创建表（会删除原有数据）
         * update: 更新表结构（推荐）
         * add: 只新增字段和表
         */
        private String auto = "update";
        
        public String getAuto() {
            return auto;
        }
        
        public void setAuto(String auto) {
            this.auto = auto;
        }
    }
    
    /**
     * 模型配置内部类
     */
    public static class Model {
        /**
         * 实体类包路径，多个包用逗号分隔
         */
        private String pack = "com.shanhai.service.entity,com.shanhai.common.crawler.model.config";
        
        public String getPack() {
            return pack;
        }
        
        public void setPack(String pack) {
            this.pack = pack;
        }
    }
}
