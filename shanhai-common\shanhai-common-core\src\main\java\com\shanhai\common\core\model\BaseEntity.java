package com.shanhai.common.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseEntity extends Model implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(name = "id", comment = "主键", type = MySqlTypeConstant.VARCHAR, length = 32, isKey = true, isNull = false)
    private String id;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "create_user", comment = "创建人", type = MySqlTypeConstant.VARCHAR, length = 32)
    private String createUser;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME)
    private Date createTime;

    @TableField(fill = FieldFill.UPDATE)
    @Column(name = "update_user", comment = "修改人", type = MySqlTypeConstant.VARCHAR, length = 32)
    private String updateUser;

    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_time", comment = "修改时间", type = MySqlTypeConstant.DATETIME)
    private Date updateTime;

    @Version
    @Column(name = "version", comment = "版本", type = MySqlTypeConstant.BIGINT, length = 10)
    private Integer version = 0;

    @TableField(fill = FieldFill.INSERT)
    @TableLogic(value = "0", delval = "1")
    @Column(name = "deleted", comment = "逻辑删除(0.正常;1.删除)", type = MySqlTypeConstant.BIGINT, length = 1)
    private Integer deleted = 0;

    @Column(name = "remark", comment = "备注", type = MySqlTypeConstant.VARCHAR, length = 50)
    private String remark;

    /**
     * 搜索值
     */
    @JsonIgnore
    @TableField(exist = false)
    private String searchValue;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

}
