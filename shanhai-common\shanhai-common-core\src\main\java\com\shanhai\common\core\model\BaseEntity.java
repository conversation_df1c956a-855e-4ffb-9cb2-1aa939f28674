package com.shanhai.common.core.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseEntity extends Model implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(name = "id", comment = "主键ID", type = MySqlTypeConstant.VARCHAR, length = 32, isKey = true, isNull = false)
    private String id;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "create_user", comment = "创建人ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String createUser;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME, isNull = true)
    private Date createTime;

    @TableField(fill = FieldFill.UPDATE)
    @Column(name = "update_user", comment = "修改人ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String updateUser;

    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time", comment = "修改时间", type = MySqlTypeConstant.DATETIME, isNull = true)
    private Date updateTime;

    @Version
    @TableField(fill = FieldFill.INSERT)
    @Column(name = "version", comment = "乐观锁版本号", type = MySqlTypeConstant.BIGINT, length = 20, isNull = false, defaultValue = "0")
    private Long version = 0L;

    @TableField(fill = FieldFill.INSERT)
    @TableLogic(value = "0", delval = "1")
    @Column(name = "deleted", comment = "逻辑删除标识(0:正常,1:删除)", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "0")
    private Integer deleted = 0;

    @Column(name = "remark", comment = "备注信息", type = MySqlTypeConstant.VARCHAR, length = 500, isNull = true)
    private String remark;

    /**
     * 搜索值
     */
    @JsonIgnore
    @TableField(exist = false)
    private String searchValue;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }
}
