package com.shanhai.common.crawler.manager;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import com.shanhai.common.crawler.service.NovelCrawlerRuleService;
import com.shanhai.common.crawler.utils.CrawlerValidator;
import com.shanhai.common.core.utils.JsonUtils;
import com.shanhai.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import cn.hutool.core.io.IoUtil;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 规则管理器
 * <p>
 * 统一管理爬虫规则的加载、缓存、验证和更新
 * 支持从数据库、配置文件等多种数据源加载规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RuleManager {

    private final NovelCrawlerRuleService ruleService;
    private final CrawlerValidator validator;
    private final Map<String, NovelCrawlerRule> ruleCache = new ConcurrentHashMap<>();
    private final Map<String, Long> ruleCacheTime = new ConcurrentHashMap<>();
    
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000L; // 5分钟缓存过期

    @Autowired
    public RuleManager(NovelCrawlerRuleService ruleService, CrawlerValidator validator) {
        this.ruleService = ruleService;
        this.validator = validator;
    }

    /**
     * 初始化规则管理器
     * 启动时加载默认规则和刷新缓存
     */
    @PostConstruct
    public void init() {
        log.info("初始化规则管理器...");
        try {
            // 加载默认规则
            loadDefaultRules();

            // 尝试刷新缓存，如果数据库表不存在则跳过
            try {
                refreshCache();
                log.info("规则管理器初始化完成，共加载{}个规则", ruleCache.size());
            } catch (Exception e) {
                log.warn("数据库规则加载失败，可能是表尚未创建: {}", e.getMessage());
                log.info("规则管理器初始化完成，仅加载了默认规则 {} 个", ruleCache.size());
            }
        } catch (Exception e) {
            log.error("规则管理器初始化失败", e);
            // 不抛出异常，允许应用继续启动
            log.warn("规则管理器将以降级模式运行，仅使用默认规则");
        }
    }

    /**
     * 根据站点名称获取规则
     */
    public NovelCrawlerRule getRuleBySourceName(String sourceName) {
        if (StringUtils.isBlank(sourceName)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点名称不能为空");
        }

        // 先从缓存获取
        NovelCrawlerRule cachedRule = getCachedRule(sourceName);
        if (cachedRule != null) {
            return cachedRule;
        }

        // 从数据库获取
        NovelCrawlerRule rule = ruleService.loadAllRules().stream()
                .filter(r -> sourceName.equals(r.getSourceName()))
                .findFirst()
                .orElse(null);

        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.RULE_NOT_FOUND, "未找到站点规则: " + sourceName);
        }

        // 验证规则
        validator.validateRule(rule);

        // 加入缓存
        putCache(sourceName, rule);

        return rule;
    }

    /**
     * 获取所有可用的规则
     */
    public List<NovelCrawlerRule> getAllRules() {
        return ruleService.loadAllRules().stream()
                .filter(this::isRuleValid)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有站点名称
     */
    public List<String> getAllSourceNames() {
        return getAllRules().stream()
                .map(NovelCrawlerRule::getSourceName)
                .collect(Collectors.toList());
    }

    /**
     * 保存或更新规则
     */
    public boolean saveOrUpdateRule(NovelCrawlerRule rule) {
        try {
            // 验证规则
            validator.validateRule(rule);

            // 保存到数据库
            boolean success;
            if (StringUtils.isBlank(rule.getId())) {
                success = ruleService.saveRule(rule);
            } else {
                success = ruleService.updateRule(rule);
            }

            if (success) {
                // 更新缓存
                putCache(rule.getSourceName(), rule);
                log.info("规则保存成功: {}", rule.getSourceName());
            }

            return success;
        } catch (Exception e) {
            log.error("保存规则失败: {}", rule.getSourceName(), e);
            throw new CrawlerException(CrawlerErrorCode.RULE_CONFIG_ERROR, "保存规则失败: " + e.getMessage());
        }
    }

    /**
     * 删除规则
     */
    public boolean deleteRule(String sourceName) {
        try {
            NovelCrawlerRule rule = getRuleBySourceName(sourceName);
            boolean success = ruleService.deleteRuleById(rule.getId());
            
            if (success) {
                // 从缓存中移除
                removeFromCache(sourceName);
                log.info("规则删除成功: {}", sourceName);
            }
            
            return success;
        } catch (Exception e) {
            log.error("删除规则失败: {}", sourceName, e);
            return false;
        }
    }

    /**
     * 刷新缓存
     */
    public void refreshCache() {
        log.info("刷新规则缓存...");
        ruleCache.clear();
        ruleCacheTime.clear();
        
        List<NovelCrawlerRule> rules = ruleService.loadAllRules();
        for (NovelCrawlerRule rule : rules) {
            if (isRuleValid(rule)) {
                putCache(rule.getSourceName(), rule);
            }
        }
        
        log.info("规则缓存刷新完成，共缓存{}个规则", ruleCache.size());
    }

    /**
     * 验证规则是否有效
     */
    public boolean isRuleValid(NovelCrawlerRule rule) {
        try {
            validator.validateRule(rule);
            return true;
        } catch (Exception e) {
            log.warn("规则验证失败: {} - {}", rule.getSourceName(), e.getMessage());
            return false;
        }
    }

    /**
     * 从缓存获取规则
     *
     * @param sourceName 站点名称
     * @return 缓存的规则，如果不存在或已过期则返回null
     */
    private NovelCrawlerRule getCachedRule(String sourceName) {
        Long cacheTime = ruleCacheTime.get(sourceName);

        // 检查缓存是否存在且未过期
        if (cacheTime == null || isExpired(cacheTime)) {
            removeFromCache(sourceName);
            return null;
        }

        return ruleCache.get(sourceName);
    }

    /**
     * 将规则放入缓存
     *
     * @param sourceName 站点名称
     * @param rule 规则对象
     */
    private void putCache(String sourceName, NovelCrawlerRule rule) {
        ruleCache.put(sourceName, rule);
        ruleCacheTime.put(sourceName, System.currentTimeMillis());
    }

    /**
     * 从缓存中移除规则
     *
     * @param sourceName 站点名称
     */
    private void removeFromCache(String sourceName) {
        ruleCache.remove(sourceName);
        ruleCacheTime.remove(sourceName);
    }

    /**
     * 检查缓存是否已过期
     *
     * @param cacheTime 缓存时间戳
     * @return true表示已过期
     */
    private boolean isExpired(Long cacheTime) {
        return System.currentTimeMillis() - cacheTime > CACHE_EXPIRE_TIME;
    }

    /**
     * 加载默认规则
     */
    private void loadDefaultRules() {
        try {
            loadRuleFromResource("config/novel-crawler-html.json");
            loadRuleFromResource("config/novel-crawler-api.json");
        } catch (Exception e) {
            log.warn("加载默认规则失败", e);
        }
    }

    /**
     * 从资源文件加载规则
     */
    private void loadRuleFromResource(String resourcePath) {
        try {
            ClassPathResource resource = new ClassPathResource(resourcePath);
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
                    NovelCrawlerRule rule = JsonUtils.fromJson(content, NovelCrawlerRule.class);
                    
                    if (rule != null && isRuleValid(rule)) {
                        // 检查是否已存在
                        try {
                            getRuleBySourceName(rule.getSourceName());
                            log.debug("规则已存在，跳过加载: {}", rule.getSourceName());
                        } catch (CrawlerException e) {
                            // 规则不存在，保存新规则
                            ruleService.saveRule(rule);
                            log.info("从资源文件加载规则: {}", rule.getSourceName());
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.warn("加载资源文件失败: {}", resourcePath, e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("cacheSize", ruleCache.size());
        stats.put("totalRules", ruleService.loadAllRules().size());
        stats.put("cacheHitRate", calculateCacheHitRate());
        return stats;
    }

    /**
     * 计算缓存命中率（简化实现）
     */
    private double calculateCacheHitRate() {
        // 这里可以实现更复杂的缓存命中率统计
        return ruleCache.isEmpty() ? 0.0 : 0.85; // 示例值
    }


}
