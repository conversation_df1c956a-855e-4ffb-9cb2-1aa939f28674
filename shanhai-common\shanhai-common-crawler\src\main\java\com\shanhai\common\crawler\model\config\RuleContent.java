package com.shanhai.common.crawler.model.config;

import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.util.List;

/**
 * 章节内容页解析配置
 * <p>
 * 用于描述如何从章节内容页提取正文、标题、导航等信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleContent extends BaseEntity {
    /**
     * 标题选择器
     */
    private String titleSelector;
    /**
     * 正文内容选择器
     */
    private String contentSelector;
    /**
     * 正文内容过滤正则（可选）
     */
    private String contentFilter;
    /**
     * 正文内容替换规则列表（可选）
     */
    private List<ReplaceRule> replaceRules;
    /**
     * 是否移除HTML标签（可选）
     */
    private Boolean removeHtml;
    /**
     * 需要移除的元素选择器列表（可选）
     */
    private List<String> removeSelectors;
    /**
     * 下一章选择器（可选）
     */
    private String nextChapterSelector;
    /**
     * 下一章URL属性名（可选）
     */
    private String nextChapterAttr;
    /**
     * 上一章选择器（可选）
     */
    private String prevChapterSelector;
    /**
     * 上一章URL属性名（可选）
     */
    private String prevChapterAttr;
    /**
     * 等待元素选择器（可选）
     */
    private String waitForSelector;
    /** 关联主表ID */
    private String ruleId;
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
} 