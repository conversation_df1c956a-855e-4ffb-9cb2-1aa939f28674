package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.util.List;

/**
 * 章节内容页解析配置
 * <p>
 * 用于描述如何从章节内容页提取正文、标题、导航等信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_rule_content")
@TableName("crawler_rule_content")
public class RuleContent extends BaseEntity {
    /**
     * 标题选择器
     */
    @Column(name = "title_selector", comment = "标题选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String titleSelector;

    /**
     * 正文内容选择器
     */
    @Column(name = "content_selector", comment = "正文内容选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = false)
    private String contentSelector;

    /**
     * 正文内容过滤正则（可选）
     */
    @Column(name = "content_filter", comment = "正文内容过滤正则", type = MySqlTypeConstant.TEXT, isNull = true)
    private String contentFilter;

    /**
     * 正文内容替换规则列表ID（可选）
     */
    @Column(name = "replace_rules_ids", comment = "替换规则ID列表(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private String replaceRulesIds;

    /**
     * 是否移除HTML标签（可选）
     */
    @Column(name = "remove_html", comment = "是否移除HTML标签", type = MySqlTypeConstant.TINYINT, length = 1, isNull = true, defaultValue = "1")
    private Boolean removeHtml;

    /**
     * 需要移除的元素选择器列表（可选）
     */
    @Column(name = "remove_selectors", comment = "需要移除的元素选择器列表(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private String removeSelectorsJson;

    // 以下字段不存储到数据库，通过关联查询或JSON解析获取
    private transient List<ReplaceRule> replaceRules;
    private transient List<String> removeSelectors;
    /**
     * 下一章选择器（可选）
     */
    @Column(name = "next_chapter_selector", comment = "下一章选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String nextChapterSelector;

    /**
     * 下一章URL属性名（可选）
     */
    @Column(name = "next_chapter_attr", comment = "下一章URL属性名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true, defaultValue = "href")
    private String nextChapterAttr;

    /**
     * 上一章选择器（可选）
     */
    @Column(name = "prev_chapter_selector", comment = "上一章选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String prevChapterSelector;

    /**
     * 上一章URL属性名（可选）
     */
    @Column(name = "prev_chapter_attr", comment = "上一章URL属性名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true, defaultValue = "href")
    private String prevChapterAttr;

    /**
     * 等待元素选择器（可选）
     */
    @Column(name = "wait_for_selector", comment = "等待元素选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String waitForSelector;

    /** 关联主表ID */
    @Column(name = "rule_id", comment = "关联主表ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleId;
}