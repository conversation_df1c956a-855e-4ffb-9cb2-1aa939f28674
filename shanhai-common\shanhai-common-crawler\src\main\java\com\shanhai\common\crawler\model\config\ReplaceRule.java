package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 内容替换规则
 * <p>
 * 用于对采集到的内容进行正则替换处理。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_replace_rule")
@TableName("crawler_replace_rule")
public class ReplaceRule extends BaseEntity {

    /** 匹配正则表达式 */
    @Column(name = "pattern", comment = "匹配正则表达式", type = MySqlTypeConstant.TEXT, isNull = false)
    private String pattern;

    /** 替换内容 */
    @Column(name = "replacement", comment = "替换内容", type = MySqlTypeConstant.TEXT, isNull = true)
    private String replacement;

    /** 规则名称 */
    @Column(name = "rule_name", comment = "规则名称", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = true)
    private String ruleName;

    /** 规则描述 */
    @Column(name = "rule_description", comment = "规则描述", type = MySqlTypeConstant.VARCHAR, length = 500, isNull = true)
    private String ruleDescription;

    /** 是否启用 */
    @Builder.Default
    @Column(name = "enabled", comment = "是否启用", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "1")
    private Boolean enabled = true;

    /** 排序 */
    @Builder.Default
    @Column(name = "sort_order", comment = "排序", type = MySqlTypeConstant.INT, length = 11, isNull = false, defaultValue = "0")
    private Integer sortOrder = 0;
} 