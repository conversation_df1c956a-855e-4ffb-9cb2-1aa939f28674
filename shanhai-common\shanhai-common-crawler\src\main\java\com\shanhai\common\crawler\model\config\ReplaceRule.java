package com.shanhai.common.crawler.model.config;

import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 内容替换规则
 * <p>
 * 用于对采集到的内容进行正则替换处理。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplaceRule extends BaseEntity {
    /** 匹配正则表达式 */
    private String pattern;
    /** 替换内容 */
    private String replacement;
} 