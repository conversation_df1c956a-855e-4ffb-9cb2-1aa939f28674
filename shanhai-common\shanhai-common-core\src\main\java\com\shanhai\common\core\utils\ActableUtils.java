package com.shanhai.common.core.utils;

import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.annotation.ActableEnhanced;
import com.shanhai.common.core.annotation.ColumnEnhanced;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ACTable 工具类
 * <p>
 * 提供 ACTable 相关的工具方法
 *
 * <AUTHOR>
 */
@Slf4j
public class ActableUtils {

    /**
     * 获取表名
     */
    public static String getTableName(Class<?> clazz) {
        Table table = clazz.getAnnotation(Table.class);
        return table != null ? table.name() : "";
    }

    /**
     * 获取表注释
     */
    public static String getTableComment(Class<?> clazz) {
        ActableEnhanced enhanced = clazz.getAnnotation(ActableEnhanced.class);
        return enhanced != null ? enhanced.comment() : "";
    }

    /**
     * 获取所有字段信息
     */
    public static List<ColumnInfo> getColumnInfos(Class<?> clazz) {
        List<ColumnInfo> columnInfos = new ArrayList<>();
        
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                ColumnInfo info = new ColumnInfo();
                info.setFieldName(field.getName());
                info.setColumnName(column.name());
                info.setComment(column.comment());
                info.setType(column.type());
                info.setLength(column.length());
                info.setDecimalLength(column.decimalLength());
                info.setIsKey(column.isKey());
                info.setIsNull(column.isNull());
                info.setIsAutoIncrement(column.isAutoIncrement());
                info.setDefaultValue(column.defaultValue());
                
                // 获取增强注解信息
                ColumnEnhanced enhanced = field.getAnnotation(ColumnEnhanced.class);
                if (enhanced != null) {
                    info.setLabel(enhanced.label());
                    info.setBusinessMeaning(enhanced.businessMeaning());
                    info.setSearchable(enhanced.searchable());
                    info.setSortable(enhanced.sortable());
                    info.setListVisible(enhanced.listVisible());
                    info.setDetailVisible(enhanced.detailVisible());
                    info.setFormVisible(enhanced.formVisible());
                    info.setDisplayOrder(enhanced.displayOrder());
                    info.setGroup(enhanced.group());
                    info.setSensitive(enhanced.sensitive());
                    info.setSensitiveType(enhanced.sensitiveType().name());
                }
                
                columnInfos.add(info);
            }
        }
        
        return columnInfos.stream()
                .sorted(Comparator.comparingInt(ColumnInfo::getDisplayOrder))
                .collect(Collectors.toList());
    }

    /**
     * 获取主键字段
     */
    public static List<ColumnInfo> getPrimaryKeyColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::getIsKey)
                .collect(Collectors.toList());
    }

    /**
     * 获取可搜索字段
     */
    public static List<ColumnInfo> getSearchableColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::isSearchable)
                .collect(Collectors.toList());
    }

    /**
     * 获取可排序字段
     */
    public static List<ColumnInfo> getSortableColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::isSortable)
                .collect(Collectors.toList());
    }

    /**
     * 获取列表显示字段
     */
    public static List<ColumnInfo> getListVisibleColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::isListVisible)
                .collect(Collectors.toList());
    }

    /**
     * 获取表单显示字段
     */
    public static List<ColumnInfo> getFormVisibleColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::isFormVisible)
                .collect(Collectors.toList());
    }

    /**
     * 获取敏感字段
     */
    public static List<ColumnInfo> getSensitiveColumns(Class<?> clazz) {
        return getColumnInfos(clazz).stream()
                .filter(ColumnInfo::isSensitive)
                .collect(Collectors.toList());
    }

    /**
     * 验证字段类型
     */
    public static boolean isValidColumnType(String type) {
        try {
            // 检查是否为有效的MySQL类型
            return Arrays.stream(MySqlTypeConstant.class.getFields())
                    .anyMatch(field -> {
                        try {
                            return type.equals(field.get(null));
                        } catch (IllegalAccessException e) {
                            return false;
                        }
                    });
        } catch (Exception e) {
            log.warn("验证字段类型失败: {}", type, e);
            return false;
        }
    }

    /**
     * 生成建表SQL（简化版）
     */
    public static String generateCreateTableSQL(Class<?> clazz) {
        StringBuilder sql = new StringBuilder();
        
        String tableName = getTableName(clazz);
        String tableComment = getTableComment(clazz);
        
        sql.append("CREATE TABLE IF NOT EXISTS `").append(tableName).append("` (\n");
        
        List<ColumnInfo> columns = getColumnInfos(clazz);
        List<String> primaryKeys = new ArrayList<>();
        
        for (int i = 0; i < columns.size(); i++) {
            ColumnInfo column = columns.get(i);
            sql.append("  `").append(column.getColumnName()).append("` ");
            sql.append(column.getType());
            
            if (column.getLength() > 0) {
                sql.append("(").append(column.getLength());
                if (column.getDecimalLength() > 0) {
                    sql.append(",").append(column.getDecimalLength());
                }
                sql.append(")");
            }
            
            if (!column.getIsNull()) {
                sql.append(" NOT NULL");
            }
            
            if (column.getIsAutoIncrement()) {
                sql.append(" AUTO_INCREMENT");
            }
            
            if (StringUtils.isNotBlank(column.getDefaultValue())) {
                sql.append(" DEFAULT '").append(column.getDefaultValue()).append("'");
            }
            
            if (StringUtils.isNotBlank(column.getComment())) {
                sql.append(" COMMENT '").append(column.getComment()).append("'");
            }
            
            if (column.getIsKey()) {
                primaryKeys.add(column.getColumnName());
            }
            
            if (i < columns.size() - 1 || !primaryKeys.isEmpty()) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        if (!primaryKeys.isEmpty()) {
            sql.append("  PRIMARY KEY (");
            sql.append(primaryKeys.stream()
                    .map(key -> "`" + key + "`")
                    .collect(Collectors.joining(", ")));
            sql.append(")\n");
        }
        
        sql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        if (StringUtils.isNotBlank(tableComment)) {
            sql.append(" COMMENT='").append(tableComment).append("'");
        }
        
        sql.append(";");
        
        return sql.toString();
    }

    /**
     * 字段信息类
     */
    public static class ColumnInfo {
        private String fieldName;
        private String columnName;
        private String comment;
        private String type;
        private int length;
        private int decimalLength;
        private Boolean isKey;
        private Boolean isNull;
        private Boolean isAutoIncrement;
        private String defaultValue;
        
        // 增强字段
        private String label;
        private String businessMeaning;
        private boolean searchable = true;
        private boolean sortable = true;
        private boolean listVisible = true;
        private boolean detailVisible = true;
        private boolean formVisible = true;
        private int displayOrder = 0;
        private String group;
        private boolean sensitive = false;
        private String sensitiveType;

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
        
        public int getDecimalLength() { return decimalLength; }
        public void setDecimalLength(int decimalLength) { this.decimalLength = decimalLength; }
        
        public Boolean getIsKey() { return isKey; }
        public void setIsKey(Boolean isKey) { this.isKey = isKey; }
        
        public Boolean getIsNull() { return isNull; }
        public void setIsNull(Boolean isNull) { this.isNull = isNull; }
        
        public Boolean getIsAutoIncrement() { return isAutoIncrement; }
        public void setIsAutoIncrement(Boolean isAutoIncrement) { this.isAutoIncrement = isAutoIncrement; }
        
        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }
        
        public String getLabel() { return label; }
        public void setLabel(String label) { this.label = label; }
        
        public String getBusinessMeaning() { return businessMeaning; }
        public void setBusinessMeaning(String businessMeaning) { this.businessMeaning = businessMeaning; }
        
        public boolean isSearchable() { return searchable; }
        public void setSearchable(boolean searchable) { this.searchable = searchable; }
        
        public boolean isSortable() { return sortable; }
        public void setSortable(boolean sortable) { this.sortable = sortable; }
        
        public boolean isListVisible() { return listVisible; }
        public void setListVisible(boolean listVisible) { this.listVisible = listVisible; }
        
        public boolean isDetailVisible() { return detailVisible; }
        public void setDetailVisible(boolean detailVisible) { this.detailVisible = detailVisible; }
        
        public boolean isFormVisible() { return formVisible; }
        public void setFormVisible(boolean formVisible) { this.formVisible = formVisible; }
        
        public int getDisplayOrder() { return displayOrder; }
        public void setDisplayOrder(int displayOrder) { this.displayOrder = displayOrder; }
        
        public String getGroup() { return group; }
        public void setGroup(String group) { this.group = group; }
        
        public boolean isSensitive() { return sensitive; }
        public void setSensitive(boolean sensitive) { this.sensitive = sensitive; }
        
        public String getSensitiveType() { return sensitiveType; }
        public void setSensitiveType(String sensitiveType) { this.sensitiveType = sensitiveType; }
    }
}
