-- 山海小说爬虫系统数据库初始化脚本
-- 如果 ACTable 自动建表失败，可以手动执行此脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `shanhai` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `shanhai`;

-- 1. 爬虫规则主表
CREATE TABLE IF NOT EXISTS `crawler_rule` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `source_name` VARCHAR(100) NOT NULL COMMENT '站点名称',
    `source_url` VARCHAR(500) NOT NULL COMMENT '站点主页URL',
    `user_agent` VARCHAR(500) NULL COMMENT '用户代理',
    `headers` TEXT NULL COMMENT '请求头(JSON格式)',
    `cookies` TEXT NULL COMMENT 'Cookie(JSON格式)',
    `timeout` INT NULL COMMENT '超时时间(毫秒)',
    `mode` VARCHAR(20) NOT NULL DEFAULT 'HTML' COMMENT '爬取模式(HTML/API)',
    `rule_search_id` BIGINT NULL COMMENT '搜索规则ID',
    `rule_book_info_id` BIGINT NULL COMMENT '书籍信息规则ID',
    `rule_chapter_id` BIGINT NULL COMMENT '章节规则ID',
    `rule_content_id` BIGINT NULL COMMENT '内容规则ID',
    `rule_anti_spider_id` BIGINT NULL COMMENT '反爬虫规则ID',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_source_name` (`source_name`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫规则主表';

-- 2. 搜索规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_search` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `search_url` VARCHAR(500) NOT NULL COMMENT '搜索URL模板',
    `search_method` VARCHAR(10) NOT NULL DEFAULT 'GET' COMMENT '请求方法',
    `search_params` TEXT NULL COMMENT '搜索参数(JSON格式)',
    `list_selector` VARCHAR(200) NOT NULL COMMENT '书籍列表选择器',
    `name_selector` VARCHAR(200) NOT NULL COMMENT '书名选择器',
    `name_attr` VARCHAR(50) NULL COMMENT '书名属性',
    `author_selector` VARCHAR(200) NULL COMMENT '作者选择器',
    `author_attr` VARCHAR(50) NULL COMMENT '作者属性',
    `cover_selector` VARCHAR(200) NULL COMMENT '封面选择器',
    `cover_attr` VARCHAR(50) NULL COMMENT '封面属性',
    `detail_url_selector` VARCHAR(200) NOT NULL COMMENT '详情页URL选择器',
    `detail_url_attr` VARCHAR(50) NULL COMMENT '详情页URL属性',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索规则表';

-- 3. 书籍信息规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_book_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name_selector` VARCHAR(200) NOT NULL COMMENT '书名选择器',
    `name_attr` VARCHAR(50) NULL COMMENT '书名属性',
    `author_selector` VARCHAR(200) NULL COMMENT '作者选择器',
    `author_attr` VARCHAR(50) NULL COMMENT '作者属性',
    `cover_selector` VARCHAR(200) NULL COMMENT '封面选择器',
    `cover_attr` VARCHAR(50) NULL COMMENT '封面属性',
    `intro_selector` VARCHAR(200) NULL COMMENT '简介选择器',
    `intro_attr` VARCHAR(50) NULL COMMENT '简介属性',
    `category_selector` VARCHAR(200) NULL COMMENT '分类选择器',
    `category_attr` VARCHAR(50) NULL COMMENT '分类属性',
    `status_selector` VARCHAR(200) NULL COMMENT '状态选择器',
    `status_attr` VARCHAR(50) NULL COMMENT '状态属性',
    `word_count_selector` VARCHAR(200) NULL COMMENT '字数选择器',
    `word_count_attr` VARCHAR(50) NULL COMMENT '字数属性',
    `last_update_selector` VARCHAR(200) NULL COMMENT '最后更新时间选择器',
    `last_update_attr` VARCHAR(50) NULL COMMENT '最后更新时间属性',
    `chapter_list_url_selector` VARCHAR(200) NULL COMMENT '章节列表URL选择器',
    `chapter_list_url_attr` VARCHAR(50) NULL COMMENT '章节列表URL属性',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='书籍信息规则表';

-- 4. 章节规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_chapter` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `list_selector` VARCHAR(200) NOT NULL COMMENT '章节列表选择器',
    `title_selector` VARCHAR(200) NOT NULL COMMENT '章节标题选择器',
    `title_attr` VARCHAR(50) NULL COMMENT '章节标题属性',
    `url_selector` VARCHAR(200) NOT NULL COMMENT '章节URL选择器',
    `url_attr` VARCHAR(50) NULL COMMENT '章节URL属性',
    `is_reverse` TINYINT NOT NULL DEFAULT 0 COMMENT '是否反转章节顺序',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='章节规则表';

-- 5. 内容规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_content` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `title_selector` VARCHAR(200) NULL COMMENT '标题选择器',
    `content_selector` VARCHAR(200) NOT NULL COMMENT '内容选择器',
    `content_filter` VARCHAR(500) NULL COMMENT '内容过滤规则',
    `replace_rules` TEXT NULL COMMENT '替换规则(JSON格式)',
    `remove_html` TINYINT NOT NULL DEFAULT 1 COMMENT '是否移除HTML标签',
    `remove_selectors` TEXT NULL COMMENT '需要移除的选择器(JSON格式)',
    `next_chapter_selector` VARCHAR(200) NULL COMMENT '下一章选择器',
    `next_chapter_attr` VARCHAR(50) NULL COMMENT '下一章属性',
    `prev_chapter_selector` VARCHAR(200) NULL COMMENT '上一章选择器',
    `prev_chapter_attr` VARCHAR(50) NULL COMMENT '上一章属性',
    `wait_for_selector` VARCHAR(200) NULL COMMENT '等待加载的选择器',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容规则表';

-- 6. 反爬虫规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_anti_spider` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_agents` TEXT NULL COMMENT 'User-Agent列表(JSON格式)',
    `proxy_list` TEXT NULL COMMENT '代理IP列表(JSON格式)',
    `min_delay_ms` INT NULL COMMENT '最小延迟时间(毫秒)',
    `max_delay_ms` INT NULL COMMENT '最大延迟时间(毫秒)',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反爬虫规则表';

-- 7. 替换规则表
CREATE TABLE IF NOT EXISTS `crawler_rule_replace` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
    `search_pattern` VARCHAR(500) NOT NULL COMMENT '搜索模式(正则表达式)',
    `replacement` VARCHAR(500) NOT NULL COMMENT '替换内容',
    `is_regex` TINYINT NOT NULL DEFAULT 1 COMMENT '是否为正则表达式',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_rule_name` (`rule_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='替换规则表';

-- 8. 测试实体表（如果存在）
CREATE TABLE IF NOT EXISTS `test_entity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '名称',
    `create_user` VARCHAR(64) NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(64) NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标识(0:正常,1:删除)',
    `remark` VARCHAR(500) NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试实体表';

-- 插入一些示例数据（可选）
-- 这里可以添加一些默认的爬虫规则数据

COMMIT;
