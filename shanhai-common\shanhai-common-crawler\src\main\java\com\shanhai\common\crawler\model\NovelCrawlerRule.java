package com.shanhai.common.crawler.model;

import com.shanhai.common.crawler.model.config.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 小说爬虫规则主类
 * <p>
 * 作为所有规则配置的入口，聚合各子配置。支持配置校验和业务规则检查。
 * 线程安全：仅为数据结构，线程安全。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NovelCrawlerRule {
    /**
     * 规则名称（用于标识和缓存）
     */
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;
    /**
     * 站点基础信息
     */
    @NotNull(message = "站点基础信息不能为空")
    private SourceInfo sourceInfo;
    /**
     * 采集模式（API/HTML/SELENIUM/AUTO）
     */
    @NotBlank(message = "采集模式不能为空")
    private String mode;
    /**
     * 搜索配置
     */
    @NotNull(message = "搜索配置不能为空")
    private SearchConfig searchConfig;
    /**
     * 书籍详情配置
     */
    @NotNull(message = "书籍详情配置不能为空")
    private BookInfoConfig bookInfoConfig;
    /**
     * 章节列表配置
     */
    @NotNull(message = "章节列表配置不能为空")
    private ChapterListConfig chapterListConfig;
    /**
     * 章节内容配置
     */
    @NotNull(message = "章节内容配置不能为空")
    private ChapterContentConfig chapterContentConfig;
    /**
     * 反爬虫配置（可选）
     */
    private AntiSpiderConfig antiSpiderConfig;
} 