package com.shanhai.common.core.annotation;

import java.lang.annotation.*;

/**
 * 字段增强注解
 * <p>
 * 提供额外的字段配置功能
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ColumnEnhanced {

    /**
     * 字段验证规则
     */
    String validation() default "";

    /**
     * 字段格式化规则
     */
    String format() default "";

    /**
     * 是否敏感字段（用于日志脱敏）
     */
    boolean sensitive() default false;

    /**
     * 敏感字段脱敏类型
     */
    SensitiveType sensitiveType() default SensitiveType.CUSTOM;

    /**
     * 字段业务含义描述
     */
    String businessMeaning() default "";

    /**
     * 字段数据来源
     */
    String dataSource() default "";

    /**
     * 是否可搜索
     */
    boolean searchable() default true;

    /**
     * 是否可排序
     */
    boolean sortable() default true;

    /**
     * 是否在列表中显示
     */
    boolean listVisible() default true;

    /**
     * 是否在详情中显示
     */
    boolean detailVisible() default true;

    /**
     * 是否在表单中显示
     */
    boolean formVisible() default true;

    /**
     * 字段显示顺序
     */
    int displayOrder() default 0;

    /**
     * 字段分组
     */
    String group() default "";

    /**
     * 字段标签（用于前端显示）
     */
    String label() default "";

    /**
     * 字段占位符
     */
    String placeholder() default "";

    /**
     * 字段帮助文本
     */
    String helpText() default "";

    /**
     * 字段单位
     */
    String unit() default "";

    /**
     * 最小值（数值类型）
     */
    double minValue() default Double.MIN_VALUE;

    /**
     * 最大值（数值类型）
     */
    double maxValue() default Double.MAX_VALUE;

    /**
     * 最小长度（字符串类型）
     */
    int minLength() default 0;

    /**
     * 最大长度（字符串类型）
     */
    int maxLength() default Integer.MAX_VALUE;

    /**
     * 正则表达式验证
     */
    String regex() default "";

    /**
     * 字典类型（用于下拉选择）
     */
    String dictType() default "";

    /**
     * 是否支持多选
     */
    boolean multiple() default false;

    /**
     * 默认值表达式
     */
    String defaultValueExpression() default "";

    /**
     * 计算字段表达式
     */
    String calculatedExpression() default "";

    /**
     * 是否为计算字段
     */
    boolean calculated() default false;

    /**
     * 敏感字段类型枚举
     */
    enum SensitiveType {
        CUSTOM,         // 自定义
        NAME,           // 姓名
        ID_CARD,        // 身份证
        PHONE,          // 手机号
        EMAIL,          // 邮箱
        ADDRESS,        // 地址
        BANK_CARD,      // 银行卡号
        PASSWORD        // 密码
    }
}
