package com.shanhai.common.crawler.utils;

import cn.hutool.json.JSONUtil;
import com.shanhai.common.crawler.model.NovelCrawlerRule;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.util.function.Supplier;
import org.slf4j.MDC;

/**
 * 爬虫网络管理器
 * <p>
 * 负责网络请求、重试机制、代理管理、网络状态检测等功能。
 * 统一对外提供网络相关的所有工具方法。
 * 线程安全：无状态，线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@Slf4j
public class CrawlerNetworkManager {

    // 移除非static字段，只保留static
    @Value("${crawler.network.timeout:10000}")
    private int timeoutConfig;
    @Value("${crawler.network.retry:3}")
    private int retryConfig;
    @Value("${crawler.network.user-agent-pool:}")
    private List<String> userAgentPoolConfig;
    @Value("${crawler.network.proxy-pool:}")
    private List<String> proxyPoolConfig;
    private static int timeout;
    private static int retry;
    private static List<String> userAgentPool;
    private static List<String> proxyPool;
    private static String defaultUserAgent;
    @PostConstruct
    public void init() {
        timeout = timeoutConfig;
        retry = retryConfig;
        userAgentPool = userAgentPoolConfig;
        proxyPool = proxyPoolConfig;
        if (userAgentPool != null && !userAgentPool.isEmpty()) {
            defaultUserAgent = userAgentPool.get(0);
        } else {
            defaultUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        }
    }

    // ================== 网络请求方法 ==================

    /**
     * 发送GET请求
     *
     * @param url    请求URL
     * @param config 爬虫配置
     * @return 响应文档
     */
    public static Document getDocument(String url, NovelCrawlerRule config) throws IOException {
        return getDocument(url, config, null);
    }

    /**
     * 发送GET请求（带重试）
     *
     * @param url     请求URL
     * @param config  爬虫配置
     * @param headers 请求头
     * @return 响应文档
     */
    public static Document getDocument(String url, NovelCrawlerRule config, Map<String, String> headers) {
        return retry(() -> {
            try {
                Connection conn = Jsoup.connect(url)
                        .timeout(timeout)
                        .userAgent(defaultUserAgent)
                        .followRedirects(true)
                        .ignoreContentType(true);
                applyAntiSpiderStrategy(conn, config);
                if (headers != null) {
                    headers.forEach(conn::header);
                }
                return conn.get();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, retry);
    }

    /**
     * 发送POST请求
     *
     * @param url    请求URL
     * @param data   请求数据
     * @param config 爬虫配置
     * @return 响应文档
     */
    public static Document postDocument(String url, Map<String, String> data, NovelCrawlerRule config) {
        return retry(() -> {
            try {
                Connection conn = Jsoup.connect(url)
                        .timeout(timeout)
                        .userAgent(defaultUserAgent)
                        .followRedirects(true)
                        .ignoreContentType(true);
                applyAntiSpiderStrategy(conn, config);
                if (data != null) {
                    conn.data(data);
                }
                return conn.post();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, retry);
    }

    /**
     * 通用网络请求，自动识别返回类型（HTML/JSONObject/JSONArray）
     *
     * @param url     请求URL
     * @param config  爬虫配置
     * @param headers 请求头，可为null
     * @return JSONObject/JSONArray/Document
     */
    public static Object fetchAutoType(String url, NovelCrawlerRule config, Map<String, String> headers) {
        try {
            Connection conn = Jsoup.connect(url)
                    .timeout(timeout)
                    .userAgent(defaultUserAgent)
                    .followRedirects(true)
                    .ignoreContentType(true);
            applyAntiSpiderStrategy(conn, config);
            if (headers != null) {
                headers.forEach(conn::header);
            }
            Connection.Response response = conn.execute();
            String body = response.body();
            if (body == null) return null;
            String trim = body.trim();
            try {
                if (trim.startsWith("{") && trim.endsWith("}")) {
                    return JSONUtil.parseObj(trim);
                } else if (trim.startsWith("[") && trim.endsWith("]")) {
                    return JSONUtil.parseArray(trim);
                }
            } catch (Exception e) {
                log.warn("内容JSON解析失败，尝试按HTML处理: {}", e.getMessage());
            }
            return Jsoup.parse(trim);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    // ================== 重试机制 ==================

    /**
     * 简单重试方法
     *
     * @param supplier   执行函数
     * @param maxRetries 最大重试次数
     * @return 执行结果
     */
    public static <T> T retry(Supplier<T> supplier, int maxRetries) {
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                return supplier.get();
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw new RuntimeException("网络请求失败", e);
                }
            }
        }
        throw new RuntimeException("网络请求失败");
    }

    // ================== 反爬虫策略 ==================

    /**
     * 应用反爬虫策略
     *
     * @param conn   连接对象
     * @param config 爬虫配置
     */
    private static void applyAntiSpiderStrategy(Connection conn, NovelCrawlerRule config) {
        if (config == null || config.getAntiSpiderConfig() == null) {
            return;
        }
        if (config.getSourceInfo() != null && config.getSourceInfo().getUserAgent() != null) {
            conn.userAgent(config.getSourceInfo().getUserAgent());
        }
        if (config.getAntiSpiderConfig().getProxyList() != null && !config.getAntiSpiderConfig().getProxyList().isEmpty()) {
            log.debug("应用代理策略");
        }
        if (config.getAntiSpiderConfig().getMinDelayMs() != null && config.getAntiSpiderConfig().getMaxDelayMs() != null) {
            try {
                long delay = config.getAntiSpiderConfig().getMinDelayMs() +
                        (long) (Math.random() * (config.getAntiSpiderConfig().getMaxDelayMs() - config.getAntiSpiderConfig().getMinDelayMs()));
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // ================== 网络工具方法 ==================

    /**
     * 检查URL是否可访问
     *
     * @param url URL地址
     * @return 是否可访问
     */
    public static boolean isUrlAccessible(String url) {
        try {
            Connection.Response response = Jsoup.connect(url)
                    .timeout(5000)
                    .method(Connection.Method.HEAD)
                    .execute();
            return response.statusCode() == 200;
        } catch (Exception e) {
            log.debug("URL不可访问: {}, 错误: {}", url, e.getMessage());
            return false;
        }
    }

    /**
     * 获取URL响应状态
     *
     * @param url URL地址
     * @return 响应状态码
     */
    public static int getUrlStatus(String url) {
        try {
            Connection.Response response = Jsoup.connect(url)
                    .timeout(10000)
                    .method(Connection.Method.HEAD)
                    .execute();
            return response.statusCode();
        } catch (Exception e) {
            log.error("获取URL状态失败: {}, 错误: {}", url, e.getMessage());
            return -1;
        }
    }

    /**
     * 下载文件
     *
     * @param url        文件URL
     * @param outputPath 输出路径
     * @return 是否下载成功
     */
    public static boolean downloadFile(String url, String outputPath) {
        try {
            Connection.Response response = Jsoup.connect(url)
                    .timeout(30000)
                    .ignoreContentType(true)
                    .execute();
            java.nio.file.Files.write(java.nio.file.Paths.get(outputPath), response.bodyAsBytes());
            log.info("文件下载成功: {} -> {}", url, outputPath);
            return true;
        } catch (Exception e) {
            log.error("文件下载失败: {}, 错误: {}", url, e.getMessage());
            return false;
        }
    }
} 