package com.shanhai.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * ACTable 配置类
 * <p>
 * 统一管理 ACTable 相关配置参数
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "actable")
public class ActableConfig {

    /**
     * 是否开启自动建表
     */
    private boolean autoCreateTable = true;

    /**
     * 是否开启自动更新表结构
     */
    private boolean autoUpdateTable = true;

    /**
     * 是否开启自动删除表
     */
    private boolean autoDropTable = false;

    /**
     * 表前缀
     */
    private String tablePrefix = "";

    /**
     * 表后缀
     */
    private String tableSuffix = "";

    /**
     * 是否开启驼峰命名转换
     */
    private boolean camelCase = true;

    /**
     * 是否开启下划线命名
     */
    private boolean underscore = true;

    /**
     * 是否开启注释
     */
    private boolean comment = true;

    /**
     * 是否开启索引
     */
    private boolean index = true;

    /**
     * 是否开启外键
     */
    private boolean foreignKey = false;

    /**
     * 是否开启唯一约束
     */
    private boolean unique = true;

    /**
     * 是否开启非空约束
     */
    private boolean notNull = true;

    /**
     * 是否开启默认值
     */
    private boolean defaultValue = true;

    /**
     * 是否开启自增
     */
    private boolean autoIncrement = true;

    /**
     * 是否开启主键
     */
    private boolean primaryKey = true;

    /**
     * 数据库类型
     */
    private String databaseType = "mysql";

    /**
     * 字符集
     */
    private String charset = "utf8mb4";

    /**
     * 排序规则
     */
    private String collate = "utf8mb4_unicode_ci";

    /**
     * 存储引擎
     */
    private String engine = "InnoDB";

    /**
     * 是否开启严格模式
     */
    private boolean strictMode = true;

    /**
     * 是否开启调试模式
     */
    private boolean debug = false;

    /**
     * 扫描的实体包路径
     */
    private String[] modelPackages = {
        "com.shanhai.**.model",
        "com.shanhai.**.entity",
        "com.shanhai.service.entity"
    };

    /**
     * 排除的表名模式
     */
    private String[] excludeTablePatterns = {
        "temp_*",
        "test_*",
        "*_backup"
    };

    /**
     * 备份表前缀
     */
    private String backupTablePrefix = "bak_";

    /**
     * 是否开启表备份
     */
    private boolean enableBackup = false;

    /**
     * 最大备份数量
     */
    private int maxBackupCount = 5;

    /**
     * 验证配置参数
     */
    public void validate() {
        if (modelPackages == null || modelPackages.length == 0) {
            throw new IllegalArgumentException("实体包路径不能为空");
        }
        
        if (maxBackupCount < 0) {
            throw new IllegalArgumentException("最大备份数量不能小于0");
        }
        
        if (tablePrefix != null && tablePrefix.length() > 10) {
            throw new IllegalArgumentException("表前缀长度不能超过10个字符");
        }
        
        if (tableSuffix != null && tableSuffix.length() > 10) {
            throw new IllegalArgumentException("表后缀长度不能超过10个字符");
        }
    }

    /**
     * 获取完整的表名
     */
    public String getFullTableName(String tableName) {
        StringBuilder fullName = new StringBuilder();
        
        if (tablePrefix != null && !tablePrefix.isEmpty()) {
            fullName.append(tablePrefix);
        }
        
        fullName.append(tableName);
        
        if (tableSuffix != null && !tableSuffix.isEmpty()) {
            fullName.append(tableSuffix);
        }
        
        return fullName.toString();
    }

    /**
     * 检查表名是否被排除
     */
    public boolean isTableExcluded(String tableName) {
        if (excludeTablePatterns == null || excludeTablePatterns.length == 0) {
            return false;
        }
        
        for (String pattern : excludeTablePatterns) {
            if (tableName.matches(pattern.replace("*", ".*"))) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取备份表名
     */
    public String getBackupTableName(String originalTableName) {
        return backupTablePrefix + originalTableName + "_" + System.currentTimeMillis();
    }
}
