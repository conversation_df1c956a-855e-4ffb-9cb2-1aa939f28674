package com.shanhai.common.crawler.exception;

/**
 * 爬虫错误码枚举
 * 用于统一管理爬虫相关的错误类型。
 */
public enum CrawlerErrorCode {
    UNKNOWN_ERROR("未知错误"),
    NETWORK_ERROR("网络异常"),
    PARSE_ERROR("解析失败"),
    ANTI_SPIDER_BLOCKED("被反爬拦截"),
    CONFIG_ERROR("配置错误");

    private final String message;

    CrawlerErrorCode(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
} 