package com.shanhai.common.crawler.exception;

/**
 * 爬虫错误码枚举，统一管理所有爬虫相关错误类型
 *
 * <AUTHOR>
 */
public enum CrawlerErrorCode {
    /** 网络异常 */
    NET_ERROR("网络异常"),
    /** 解析异常 */
    PARSE_ERROR("解析异常"),
    /** 参数校验异常 */
    VALIDATION_ERROR("参数校验异常"),
    /** 反爬虫拦截 */
    ANTI_SPIDER("反爬虫拦截"),
    /** 未知异常 */
    UNKNOWN_ERROR("未知异常");

    private final String desc;

    CrawlerErrorCode(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
} 