package com.shanhai.common.core.manager;

import com.shanhai.common.core.config.ActableConfig;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.manager.handler.StartUpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ACTable 管理器
 * <p>
 * 统一管理 ACTable 的初始化、表结构同步、监控等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(1000) // 确保在其他组件之后执行
public class ActableManager implements CommandLineRunner {

    private final ActableConfig actableConfig;
    private final ApplicationContext applicationContext;
    private final StartUpHandler startUpHandler;

    @Autowired
    public ActableManager(ActableConfig actableConfig, 
                         ApplicationContext applicationContext,
                         StartUpHandler startUpHandler) {
        this.actableConfig = actableConfig;
        this.applicationContext = applicationContext;
        this.startUpHandler = startUpHandler;
    }

    @PostConstruct
    public void init() {
        log.info("初始化 ACTable 管理器...");
        
        // 验证配置
        try {
            actableConfig.validate();
            log.info("ACTable 配置验证通过");
        } catch (Exception e) {
            log.error("ACTable 配置验证失败: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始执行 ACTable 表结构同步...");
        
        try {
            // 扫描实体类
            List<Class<?>> entityClasses = scanEntityClasses();
            log.info("扫描到 {} 个实体类", entityClasses.size());
            
            // 打印实体类信息
            if (actableConfig.isDebug()) {
                printEntityInfo(entityClasses);
            }
            
            // 执行表结构同步
            syncTableStructure();
            
            // 生成统计报告
            generateSyncReport();
            
            log.info("ACTable 表结构同步完成");
            
        } catch (Exception e) {
            log.error("ACTable 表结构同步失败", e);
            throw e;
        }
    }

    /**
     * 扫描实体类
     */
    private List<Class<?>> scanEntityClasses() {
        List<Class<?>> entityClasses = new ArrayList<>();
        
        try {
            // 获取所有Bean
            String[] beanNames = applicationContext.getBeanDefinitionNames();
            
            for (String beanName : beanNames) {
                try {
                    Object bean = applicationContext.getBean(beanName);
                    Class<?> clazz = bean.getClass();
                    
                    // 检查是否有@Table注解
                    if (clazz.isAnnotationPresent(Table.class)) {
                        entityClasses.add(clazz);
                    }
                } catch (Exception e) {
                    // 忽略获取Bean失败的情况
                }
            }
            
            // 通过包扫描补充
            entityClasses.addAll(scanPackages());
            
        } catch (Exception e) {
            log.warn("扫描实体类时发生异常", e);
        }
        
        return entityClasses.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 包扫描
     */
    private List<Class<?>> scanPackages() {
        List<Class<?>> classes = new ArrayList<>();
        
        // 这里可以实现包扫描逻辑
        // 由于复杂性，这里简化处理
        
        return classes;
    }

    /**
     * 打印实体类信息
     */
    private void printEntityInfo(List<Class<?>> entityClasses) {
        log.debug("=== 实体类信息 ===");
        
        for (Class<?> clazz : entityClasses) {
            Table tableAnnotation = clazz.getAnnotation(Table.class);
            if (tableAnnotation != null) {
                log.debug("实体类: {} -> 表名: {}", clazz.getSimpleName(), tableAnnotation.name());
                
                // 打印字段信息
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (field.isAnnotationPresent(com.gitee.sunchenbin.mybatis.actable.annotation.Column.class)) {
                        com.gitee.sunchenbin.mybatis.actable.annotation.Column column = 
                            field.getAnnotation(com.gitee.sunchenbin.mybatis.actable.annotation.Column.class);
                        log.debug("  字段: {} -> 列名: {}, 类型: {}, 长度: {}", 
                            field.getName(), column.name(), column.type(), column.length());
                    }
                }
            }
        }
        
        log.debug("=== 实体类信息结束 ===");
    }

    /**
     * 执行表结构同步
     */
    private void syncTableStructure() {
        try {
            if (actableConfig.isAutoCreateTable() || actableConfig.isAutoUpdateTable()) {
                log.info("开始同步表结构...");
                
                // 调用 ACTable 的同步方法
                startUpHandler.startHandler();
                
                log.info("表结构同步完成");
            } else {
                log.info("表结构同步已禁用");
            }
        } catch (Exception e) {
            log.error("表结构同步失败", e);
            throw new RuntimeException("表结构同步失败", e);
        }
    }

    /**
     * 生成同步报告
     */
    private void generateSyncReport() {
        try {
            Map<String, Object> report = new HashMap<>();
            report.put("syncTime", new Date());
            report.put("autoCreateTable", actableConfig.isAutoCreateTable());
            report.put("autoUpdateTable", actableConfig.isAutoUpdateTable());
            report.put("autoDropTable", actableConfig.isAutoDropTable());
            report.put("tablePrefix", actableConfig.getTablePrefix());
            report.put("tableSuffix", actableConfig.getTableSuffix());
            
            log.info("=== ACTable 同步报告 ===");
            log.info("同步时间: {}", report.get("syncTime"));
            log.info("自动建表: {}", report.get("autoCreateTable"));
            log.info("自动更新: {}", report.get("autoUpdateTable"));
            log.info("自动删除: {}", report.get("autoDropTable"));
            log.info("表前缀: {}", report.get("tablePrefix"));
            log.info("表后缀: {}", report.get("tableSuffix"));
            log.info("=== 同步报告结束 ===");
            
        } catch (Exception e) {
            log.warn("生成同步报告失败", e);
        }
    }

    /**
     * 手动触发表结构同步
     */
    public void manualSync() {
        log.info("手动触发表结构同步...");
        try {
            syncTableStructure();
            generateSyncReport();
            log.info("手动同步完成");
        } catch (Exception e) {
            log.error("手动同步失败", e);
            throw new RuntimeException("手动同步失败", e);
        }
    }

    /**
     * 获取配置信息
     */
    public Map<String, Object> getConfigInfo() {
        Map<String, Object> config = new HashMap<>();
        config.put("autoCreateTable", actableConfig.isAutoCreateTable());
        config.put("autoUpdateTable", actableConfig.isAutoUpdateTable());
        config.put("autoDropTable", actableConfig.isAutoDropTable());
        config.put("tablePrefix", actableConfig.getTablePrefix());
        config.put("tableSuffix", actableConfig.getTableSuffix());
        config.put("camelCase", actableConfig.isCamelCase());
        config.put("underscore", actableConfig.isUnderscore());
        config.put("comment", actableConfig.isComment());
        config.put("index", actableConfig.isIndex());
        config.put("foreignKey", actableConfig.isForeignKey());
        config.put("unique", actableConfig.isUnique());
        config.put("notNull", actableConfig.isNotNull());
        config.put("defaultValue", actableConfig.isDefaultValue());
        config.put("autoIncrement", actableConfig.isAutoIncrement());
        config.put("primaryKey", actableConfig.isPrimaryKey());
        config.put("databaseType", actableConfig.getDatabaseType());
        config.put("charset", actableConfig.getCharset());
        config.put("collate", actableConfig.getCollate());
        config.put("engine", actableConfig.getEngine());
        config.put("strictMode", actableConfig.isStrictMode());
        config.put("debug", actableConfig.isDebug());
        config.put("modelPackages", actableConfig.getModelPackages());
        config.put("excludeTablePatterns", actableConfig.getExcludeTablePatterns());
        return config;
    }

    /**
     * 检查表是否存在
     */
    public boolean isTableExists(String tableName) {
        // 这里可以实现表存在性检查逻辑
        // 简化处理，返回true
        return true;
    }

    /**
     * 获取表结构信息
     */
    public Map<String, Object> getTableStructure(String tableName) {
        // 这里可以实现获取表结构的逻辑
        Map<String, Object> structure = new HashMap<>();
        structure.put("tableName", tableName);
        structure.put("columns", new ArrayList<>());
        structure.put("indexes", new ArrayList<>());
        return structure;
    }
}
