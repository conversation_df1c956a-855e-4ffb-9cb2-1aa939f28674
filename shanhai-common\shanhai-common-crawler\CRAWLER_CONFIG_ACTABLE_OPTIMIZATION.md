# 爬虫配置实体类 ACTable 注解优化

## 优化概述

为 `shanhai-common-crawler` 模块中的配置实体类添加了完整的 ACTable 注解支持，使其能够自动创建数据库表结构，实现配置的持久化存储。

## 优化的实体类

### 1. NovelCrawlerRule (主配置表)
- **表名**: `crawler_rule`
- **功能**: 爬虫规则主配置表
- **关键字段**:
  - `source_name`: 站点名称
  - `source_url`: 站点URL
  - `mode`: 采集模式
  - `rule_search_id`: 搜索配置ID（关联）
  - `rule_book_info_id`: 书籍详情配置ID（关联）
  - `rule_chapter_id`: 章节配置ID（关联）
  - `rule_content_id`: 内容配置ID（关联）
  - `rule_anti_spider_id`: 反爬虫配置ID（关联）

### 2. RuleSearch (搜索配置表)
- **表名**: `crawler_rule_search`
- **功能**: 搜索页面解析配置
- **关键字段**:
  - `url`: 搜索接口URL
  - `book_list_selector`: 书籍列表选择器
  - `name_selector`: 书名选择器
  - `author_selector`: 作者选择器
  - `book_url_selector`: 书籍详情页URL选择器

### 3. RuleBookInfo (书籍详情配置表)
- **表名**: `crawler_rule_book_info`
- **功能**: 书籍详情页解析配置
- **关键字段**:
  - `name_selector`: 书名选择器
  - `author_selector`: 作者选择器
  - `intro_selector`: 简介选择器
  - `chapter_list_url_selector`: 章节目录URL选择器

### 4. RuleChapter (章节配置表)
- **表名**: `crawler_rule_chapter`
- **功能**: 章节列表页解析配置
- **关键字段**:
  - `chapter_item_selector`: 章节项选择器
  - `chapter_name_selector`: 章节名称选择器
  - `chapter_url_selector`: 章节URL选择器
  - `reverse_order`: 是否倒序

### 5. RuleContent (内容配置表)
- **表名**: `crawler_rule_content`
- **功能**: 章节内容页解析配置
- **关键字段**:
  - `title_selector`: 标题选择器
  - `content_selector`: 正文内容选择器
  - `content_filter`: 内容过滤正则
  - `remove_html`: 是否移除HTML标签

### 6. RuleAntiSpider (反爬虫配置表)
- **表名**: `crawler_rule_anti_spider`
- **功能**: 反爬虫策略配置
- **关键字段**:
  - `user_agents`: User-Agent列表（JSON格式）
  - `proxy_list`: 代理IP列表（JSON格式）
  - `min_delay_ms`: 最小延迟时间
  - `max_delay_ms`: 最大延迟时间

### 7. ReplaceRule (替换规则表)
- **表名**: `crawler_replace_rule`
- **功能**: 内容替换规则配置
- **关键字段**:
  - `pattern`: 匹配正则表达式
  - `replacement`: 替换内容
  - `rule_name`: 规则名称
  - `enabled`: 是否启用

## 数据库设计特点

### 1. 表结构设计
- **继承BaseEntity**: 所有表都继承基础实体，包含通用字段
- **关联设计**: 主表通过ID关联子表，支持配置的模块化管理
- **JSON存储**: 复杂数据结构使用JSON格式存储，便于扩展

### 2. 字段设计规范
- **命名规范**: 使用下划线分隔的小写字母命名
- **类型选择**: 根据数据特点选择合适的MySQL类型
- **长度设置**: 合理设置字段长度，避免浪费空间
- **默认值**: 为关键字段设置合理的默认值
- **注释完整**: 每个字段都有详细的业务注释

### 3. 约束设计
- **非空约束**: 关键字段设置非空约束
- **默认值**: 状态字段设置默认值
- **长度限制**: 字符串字段设置合理长度限制

## 表关系设计

```
crawler_rule (主表)
├── rule_search_id → crawler_rule_search
├── rule_book_info_id → crawler_rule_book_info  
├── rule_chapter_id → crawler_rule_chapter
├── rule_content_id → crawler_rule_content
└── rule_anti_spider_id → crawler_rule_anti_spider

crawler_replace_rule (独立表)
```

## 使用示例

### 1. 实体类定义
```java
@Table(name = "crawler_rule")
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    
    @Column(name = "source_name", comment = "站点名称", 
            type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String sourceName;
    
    // 其他字段...
}
```

### 2. 自动建表
启动应用时，ACTable 会自动根据注解创建对应的数据库表结构。

### 3. 数据操作
```java
// 创建爬虫规则
NovelCrawlerRule rule = NovelCrawlerRule.builder()
    .sourceName("笔趣阁")
    .sourceUrl("https://www.biquge.com")
    .mode("HTML")
    .build();
```

## 优化效果

### 1. 数据持久化
- 爬虫配置可以持久化存储到数据库
- 支持配置的增删改查操作
- 配置变更可以实时生效

### 2. 配置管理
- 支持多站点配置管理
- 配置模块化，便于维护
- 支持配置的版本控制

### 3. 扩展性
- 表结构支持动态扩展
- JSON字段支持复杂数据结构
- 预留扩展字段

### 4. 维护性
- 清晰的表结构设计
- 完整的字段注释
- 标准化的命名规范

## 注意事项

### 1. 数据迁移
- 现有配置需要迁移到数据库
- 注意数据格式转换
- 保持配置的完整性

### 2. 性能考虑
- 合理使用索引
- 避免过度关联查询
- 考虑缓存策略

### 3. 兼容性
- 保持与现有代码的兼容
- 渐进式迁移
- 向后兼容支持

## 后续优化建议

1. **索引优化**: 为常用查询字段添加索引
2. **缓存机制**: 实现配置缓存，提升查询性能
3. **配置校验**: 添加配置有效性校验
4. **版本管理**: 支持配置的版本控制和回滚
5. **可视化管理**: 提供Web界面管理配置

## 总结

通过为爬虫配置实体类添加 ACTable 注解，实现了：
- 自动化的数据库表结构管理
- 配置的持久化存储
- 模块化的配置设计
- 标准化的数据库设计

这为爬虫系统的配置管理提供了坚实的数据基础，提升了系统的可维护性和扩展性。
