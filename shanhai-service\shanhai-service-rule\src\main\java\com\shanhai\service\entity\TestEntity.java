package com.shanhai.service.entity;

import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;

/**
 * 测试实体类
 *
 * <AUTHOR>
 */
@Table(name = "test_entity")
public class TestEntity extends BaseEntity {

    @Column(name = "name", comment = "名称", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String name;

    @Column(name = "description", comment = "描述", type = MySqlTypeConstant.TEXT, isNull = true)
    private String description;

    @Column(name = "status", comment = "状态(0:禁用,1:启用)", type = MySqlTypeConstant.TINYINT, length = 1, isNull = false, defaultValue = "1")
    private Integer status = 1;

    @Column(name = "sort_order", comment = "排序", type = MySqlTypeConstant.INT, length = 11, isNull = false, defaultValue = "0")
    private Integer sortOrder = 0;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}