package com.shanhai.service.entity;

import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;

@Table(name = "test_entity")
public class TestEntity {
    @Column(name = "id", isKey = true, type = MySqlTypeConstant.INT, length = 11)
    private Integer id;

    @Column(name = "name", type = MySqlTypeConstant.VARCHAR, length = 255)
    private String name;

    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
} 