package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AntiSpiderConfig {
    private java.util.List<String> userAgents;
    private java.util.List<String> proxyList;
    private Integer minDelayMs;
    private Integer maxDelayMs;
} 