package com.shanhai.common.crawler.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.NovelCrawlerRule;
import com.shanhai.common.crawler.repository.NovelCrawlerRuleMapper;
import org.springframework.stereotype.Service;

/**
 * 小说爬虫规则持久化服务实现类
 * <p>
 * 继承MyBatis-Plus ServiceImpl，支持自定义扩展方法。
 *
 * <AUTHOR>
 */
@Service
public class NovelCrawlerRuleServiceImpl extends ServiceImpl<NovelCrawlerRuleMapper, NovelCrawlerRule>
        implements IService<NovelCrawlerRule> {
    // 可扩展自定义方法
} 