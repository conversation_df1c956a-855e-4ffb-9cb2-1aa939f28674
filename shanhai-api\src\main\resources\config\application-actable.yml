# ACTable 配置文件
# 注意：由于 ACTable 主要支持 properties 格式配置
# 这里提供 YAML 格式的配置示例，实际使用时建议使用 actable.properties

# ================================
# ACTable 核心配置
# ================================

# 由于 ACTable 的配置格式限制，这里只能提供注释说明
# 实际配置请参考 actable.properties 文件

# 配置说明：
# mybatis.table.auto: 自动建表模式
#   - none: 不处理
#   - create: 删除并创建（危险，生产环境禁用）
#   - update: 更新表结构（推荐）
#   - add: 只新增，不删除字段

# mybatis.model.pack: 实体类包路径
#   - 支持多个包，用逗号分隔
#   - 示例：com.shanhai.service.entity,com.shanhai.common.crawler.model.config

# ================================
# 环境特定配置建议
# ================================

# 开发环境建议配置：
# mybatis.table.auto=update
# mybatis.model.pack=com.shanhai.service.entity,com.shanhai.common.crawler.model.config

# 测试环境建议配置：
# mybatis.table.auto=update
# mybatis.model.pack=com.shanhai.service.entity,com.shanhai.common.crawler.model.config

# 生产环境建议配置：
# mybatis.table.auto=none 或 add
# mybatis.model.pack=com.shanhai.service.entity,com.shanhai.common.crawler.model.config

# ================================
# 使用说明
# ================================

# 1. ACTable 主要通过 properties 文件配置
# 2. 配置文件名通常为 actable.properties
# 3. 配置文件应放在 classpath 根目录下
# 4. Spring Boot 会自动加载 application.properties 中的配置
# 5. 也可以通过 @PropertySource 注解指定配置文件

# ================================
# 注意事项
# ================================

# 1. 生产环境建议使用 none 或 add 模式，避免误删数据
# 2. create 模式会删除现有表，请谨慎使用
# 3. update 模式适合开发和测试环境
# 4. 实体类包路径必须正确，否则无法扫描到实体类
# 5. 确保数据库连接配置正确
