package com.shanhai.common.crawler.model;

import lombok.Data;

import java.util.List;

/**
 * 小说书籍实体
 * <p>
 * 用于封装书籍基本信息、章节列表等。
 * 线程安全：仅为数据结构，线程安全。
 *
 * <AUTHOR>
 */
@Data
public class NovelBook {
    /** 书名 */
    private String name;
    /** 作者 */
    private String author;
    /** 封面图片URL */
    private String coverUrl;
    /** 简介 */
    private String intro;
    /** 分类 */
    private String category;
    /** 字数 */
    private String wordCount;
    /** 最新章节 */
    private String lastChapter;
    /** 状态（连载/完结） */
    private String status;
    /** 更新时间 */
    private String updateTime;
    /** 详情页URL */
    private String bookUrl;
    /** 章节目录页URL */
    private String chapterListUrl;
    /** 来源站点名 */
    private String sourceName;
    /** 来源站点URL */
    private String sourceUrl;
    /** 章节列表 */
    private List<NovelChapter> chapters;

    /**
     * 章节实体
     * <p>
     * 用于封装单个章节的标题、内容、顺序、导航等。
     *
     * <AUTHOR>
     */
    @Data
    public static class NovelChapter {
        /** 章节标题 */
        private String title;
        /** 章节URL */
        private String url;
        /** 章节内容 */
        private String content;
        /** 正文段落列表 */
        private List<String> paragraphs;
        /** 顺序号 */
        private Integer order;
        /** 上一章URL */
        private String prevChapterUrl;
        /** 下一章URL */
        private String nextChapterUrl;
    }
} 