package com.shanhai.common.crawler.strategy;

import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;

import java.util.List;

/**
 * 爬虫采集策略接口
 * <p>
 * 统一管理不同的采集方式（API、HTML等）。
 * 实现类需保证线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface CrawlerStrategy {

    /**
     * 搜索书籍
     *
     * @param config  爬虫规则配置
     * @param keyword 搜索关键词
     * @return 匹配的书籍列表
     * @throws Exception 采集异常
     */
    List<NovelBook> searchBooks(NovelCrawlerRule config, String keyword) throws Exception;

    /**
     * 获取书籍详情
     *
     * @param config  爬虫规则配置
     * @param bookUrl 书籍详情页URL
     * @return 书籍详情对象
     * @throws Exception 采集异常
     */
    NovelBook getBookInfo(NovelCrawlerRule config, String bookUrl) throws Exception;

    /**
     * 获取章节列表
     *
     * @param config         爬虫规则配置
     * @param chapterListUrl 章节目录页URL
     * @return 章节列表
     * @throws Exception 采集异常
     */
    List<NovelBook.NovelChapter> getChapterList(NovelCrawlerRule config, String chapterListUrl) throws Exception;

    /**
     * 获取章节内容
     *
     * @param config     爬虫规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容对象
     * @throws Exception 采集异常
     */
    NovelBook.NovelChapter getChapterContent(NovelCrawlerRule config, String chapterUrl) throws Exception;

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();
} 