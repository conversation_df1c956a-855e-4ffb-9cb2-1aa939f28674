package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 书籍列表项解析配置
 * <p>
 * 用于描述如何从搜索结果或书籍列表中提取单本书籍的各项信息。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookItemConfig {
    /** 书名选择器 */
    private String nameSelector;
    /** 作者选择器 */
    private String authorSelector;
    /** 封面选择器 */
    private String coverSelector;
    /** 简介选择器 */
    private String introSelector;
    /** 分类选择器 */
    private String categorySelector;
    /** 字数选择器 */
    private String wordCountSelector;
    /** 最新章节选择器 */
    private String lastChapterSelector;
    /** 书籍详情页URL选择器 */
    private String bookUrlSelector;
    /** 书籍详情页URL属性名 */
    private String bookUrlAttr;
} 