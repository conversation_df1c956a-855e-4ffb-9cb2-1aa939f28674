<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 根据环境设置页面标题
    const title = process.env.VUE_APP_TITLE || '山海管理系统'
    document.title = title
    
    // 在控制台显示当前环境信息
    console.log(`当前环境: ${process.env.VUE_APP_ENV || 'development'}`)
    console.log(`API地址: ${process.env.VUE_APP_BASE_API || '/api'}`)
  }
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  margin: 0;
  padding: 0;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}
</style> 