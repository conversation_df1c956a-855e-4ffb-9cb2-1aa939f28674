# ACTable 无法建表问题修复总结

## 问题描述

应用启动时出现以下警告：
```
2025-07-17 23:25:10.688  WARN 69324 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 配置mybatis.table.auto错误无法识别，当前配置只支持[none/update/create/add]三种类型!
```

这表明 ACTable 的配置有问题，导致自动建表功能无法正常工作。

## 问题分析

### 根本原因
1. **配置格式问题**：YAML 格式的 ACTable 配置可能存在解析问题
2. **配置冲突**：多个配置文件中可能存在冲突的配置
3. **配置加载时机**：配置可能没有在正确的时机被加载

### ACTable 支持的模式
根据错误信息，ACTable 只支持以下四种模式：
- `none`: 不执行任何操作
- `update`: 更新表结构（推荐，不会删除数据）
- `create`: 每次启动都重新创建表（会删除原有数据）
- `add`: 只新增字段和表，不删除和修改

## 修复方案

### 1. 使用 Properties 文件配置（推荐）

#### 创建 ACTable 配置文件
**文件**: `shanhai-api/src/main/resources/actable.properties`

```properties
# MyBatis ACTable 自动建表配置
# 自动建表模式设置为 update
mybatis.table.auto=update

# 实体类包路径配置
mybatis.model.pack=com.shanhai.service.entity,com.shanhai.common.crawler.model.config
```

#### 创建配置加载类
**文件**: `shanhai-api/src/main/java/com/shanhai/api/config/ACTableConfiguration.java`

```java
@Configuration
@PropertySource("classpath:actable.properties")
public class ACTableConfiguration {
    // 这个类主要用于加载 actable.properties 配置文件
    // ACTable 会自动读取 mybatis.table.auto 和 mybatis.model.pack 配置
}
```

### 2. 清理冲突配置

#### 移除 YAML 中的 ACTable 配置
从 `application-mybatis.yml` 中移除了可能冲突的配置：

```yaml
# 移除了以下配置，避免冲突
# mybatis:
#   table:
#     auto: update
#   model:
#     pack: com.shanhai.service.entity,com.shanhai.common.crawler.model.config
```

### 3. 确认实体类注解

确保所有实体类都有正确的 ACTable 注解：

```java
@Table(name = "crawler_rule")
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    @Column(name = "source_name", comment = "站点名称", 
            type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String sourceName;
    // ...
}
```

## 验证修复

### 1. 检查启动日志
修复后，应用启动时应该看到：
```
databaseType=mysql，开始执行mysql的处理方法
```

而不应该再看到：
```
配置mybatis.table.auto错误无法识别，当前配置只支持[none/update/create/add]三种类型!
```

### 2. 检查数据库表
连接到数据库，检查表是否被创建：
```sql
USE shanhai;
SHOW TABLES LIKE 'crawler_%';

-- 应该看到以下表：
-- crawler_rule
-- crawler_rule_search
-- crawler_rule_book_info
-- crawler_rule_chapter
-- crawler_rule_content
-- crawler_rule_anti_spider
-- crawler_rule_replace
```

### 3. 检查表结构
检查表结构是否正确：
```sql
DESC crawler_rule;
```

## 故障排除

### 如果 ACTable 仍然不工作

#### 1. 检查依赖版本
确保使用正确的 ACTable 版本：
```xml
<dependency>
    <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
    <artifactId>mybatis-enhance-actable</artifactId>
    <version>1.5.0.RELEASE</version>
</dependency>
```

#### 2. 启用详细日志
在 `application-dev.yml` 中添加：
```yaml
logging:
  level:
    com.gitee.sunchenbin.mybatis.actable: DEBUG
    root: INFO
```

#### 3. 检查数据库权限
确保数据库用户有创建表的权限：
```sql
SHOW GRANTS FOR 'root'@'%';
```

#### 4. 手动执行建表脚本
如果自动建表仍然失败，可以手动执行：
```sql
-- 使用提供的 init_tables.sql 脚本
source shanhai-api/src/main/resources/sql/init_tables.sql
```

### 常见问题解决

#### 问题1：配置不生效
**解决方案**：确保 `ACTableConfiguration` 类被 Spring 扫描到：
```java
@ComponentScan({
    "com.shanhai.api.config",  // 确保包含这个包
    // ... 其他包
})
```

#### 问题2：实体类扫描不到
**解决方案**：检查实体类包路径是否正确：
```properties
mybatis.model.pack=com.shanhai.service.entity,com.shanhai.common.crawler.model.config
```

#### 问题3：数据库连接问题
**解决方案**：检查数据源配置和网络连接。

## 最佳实践建议

### 1. 环境配置策略
- **开发环境**：使用 `update` 模式，方便开发调试
- **测试环境**：使用 `update` 模式，保持数据一致性
- **生产环境**：使用 `none` 模式，通过脚本手动管理表结构

### 2. 配置管理
- **统一配置**：将 ACTable 配置集中在一个文件中
- **环境隔离**：不同环境可以使用不同的配置
- **版本控制**：将配置文件纳入版本控制

### 3. 监控和日志
- **启动检查**：在应用启动时检查表是否创建成功
- **详细日志**：启用 ACTable 的详细日志，便于问题排查
- **健康检查**：提供健康检查接口，监控数据库状态

## 配置文件结构

修复后的配置文件结构：

```
shanhai-api/src/main/resources/
├── actable.properties                    # ACTable 配置
├── application.yml                       # 主配置文件
├── application-dev.yml                   # 开发环境配置
├── application-prod.yml                  # 生产环境配置
├── config/
│   ├── application-logging.yml          # 日志配置
│   └── application-mybatis.yml          # MyBatis 配置
└── sql/
    └── init_tables.sql                   # 手动建表脚本
```

## 总结

通过以下修复措施，解决了 ACTable 无法建表的问题：

1. ✅ **使用 Properties 配置**：避免了 YAML 解析问题
2. ✅ **创建配置加载类**：确保配置被正确加载
3. ✅ **清理冲突配置**：移除了可能冲突的 YAML 配置
4. ✅ **提供备用方案**：创建了手动建表脚本

修复后，ACTable 应该能够正常工作，自动创建所需的数据库表。如果仍然有问题，可以使用提供的手动建表脚本作为备用方案。
