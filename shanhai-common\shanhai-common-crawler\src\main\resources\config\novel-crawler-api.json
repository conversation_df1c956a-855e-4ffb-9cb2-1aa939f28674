{"ruleName": "api示例书源", "mode": "API", "sourceInfo": {"sourceName": "API小说网", "sourceUrl": "https://api.example.com", "userAgent": "", "timeout": 10000}, "searchConfig": {"searchUrl": "https://api.example.com/search?keyword={keyword}", "bookListSelector": "$.data.books", "bookItemSelector": "", "bookItem": {"nameSelector": "name", "authorSelector": "author", "coverSelector": "cover", "introSelector": "intro", "bookUrlSelector": "url"}, "waitForSelector": ""}, "bookInfoConfig": {"nameSelector": "name", "authorSelector": "author", "coverSelector": "cover", "introSelector": "intro", "categorySelector": "category", "wordCountSelector": "wordCount", "lastChapterSelector": "lastChapter", "statusSelector": "status", "updateTimeSelector": "updateTime", "chapterListUrlSelector": "chapterListUrl", "chapterListUrlAttr": "", "waitForSelector": ""}, "chapterListConfig": {"chapterItemSelector": "chapters", "chapterNameSelector": "title", "chapterUrlSelector": "url", "chapterUrlAttr": "", "reverseOrder": false, "maxChapters": 0, "waitForSelector": ""}, "chapterContentConfig": {"titleSelector": "title", "contentSelector": "content", "contentFilter": "", "replaceRules": [], "removeHtml": false, "removeSelectors": [], "nextChapterSelector": "", "nextChapterAttr": "", "prevChapterSelector": "", "prevChapterAttr": "", "waitForSelector": ""}, "antiSpiderConfig": {"userAgents": [], "proxyList": [], "minDelayMs": 0, "maxDelayMs": 0}}