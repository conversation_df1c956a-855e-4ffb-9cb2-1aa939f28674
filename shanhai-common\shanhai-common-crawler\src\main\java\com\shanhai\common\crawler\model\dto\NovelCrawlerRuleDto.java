package com.shanhai.common.crawler.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 小说爬虫规则DTO
 * <p>
 * 用于前后端数据传输，包含规则主信息及各子配置。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NovelCrawlerRuleDto {
    /**
     * 规则名称（用于标识和缓存）
     */
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;
    /**
     * 站点基础信息
     */
    @NotNull(message = "站点基础信息不能为空")
    private SourceInfo sourceInfo;
    /**
     * 采集模式（API/HTML/SELENIUM/AUTO）
     */
    @NotBlank(message = "采集模式不能为空")
    private String mode;
    /**
     * 搜索配置
     */
    @NotNull(message = "搜索配置不能为空")
    private SearchConfig searchConfig;
    /**
     * 书籍详情配置
     */
    @NotNull(message = "书籍详情配置不能为空")
    private BookInfoConfig bookInfoConfig;
    /**
     * 章节列表配置
     */
    @NotNull(message = "章节列表配置不能为空")
    private ChapterListConfig chapterListConfig;
    /**
     * 章节内容配置
     */
    @NotNull(message = "章节内容配置不能为空")
    private ChapterContentConfig chapterContentConfig;
    /**
     * 反爬虫配置（可选）
     */
    private AntiSpiderConfig antiSpiderConfig;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchConfig {
        private String searchUrl;
        private String searchParam;
        private String bookListSelector;
        private String bookItemSelector;
        private BookItemConfig bookItem;
        private String waitForSelector;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SourceInfo {
        @NotBlank(message = "站点名称不能为空")
        private String sourceName;
        @NotBlank(message = "站点URL不能为空")
        private String sourceUrl;
        private String userAgent;
        private Map<String, String> headers;
        private Map<String, String> cookies;
        private Integer timeout;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChapterContentConfig {
        private String titleSelector;
        private String contentSelector;
        private String contentFilter;
        private List<ReplaceRule> replaceRules;
        private Boolean removeHtml;
        private List<String> removeSelectors;
        private String nextChapterSelector;
        private String nextChapterAttr;
        private String prevChapterSelector;
        private String prevChapterAttr;
        private String waitForSelector;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChapterListConfig {
        private String chapterItemSelector;
        private String chapterNameSelector;
        private String chapterUrlSelector;
        private String chapterUrlAttr;
        private Boolean reverseOrder;
        private Integer maxChapters;
        private String waitForSelector;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BookItemConfig {
        private String nameSelector;
        private String authorSelector;
        private String coverSelector;
        private String introSelector;
        private String categorySelector;
        private String wordCountSelector;
        private String lastChapterSelector;
        private String bookUrlSelector;
        private String bookUrlAttr;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AntiSpiderConfig {
        private List<String> userAgents;
        private List<String> proxyList;
        private Integer minDelayMs;
        private Integer maxDelayMs;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BookInfoConfig {
        private String nameSelector;
        private String authorSelector;
        private String coverSelector;
        private String introSelector;
        private String categorySelector;
        private String wordCountSelector;
        private String lastChapterSelector;
        private String statusSelector;
        private String updateTimeSelector;
        private String chapterListUrlSelector;
        private String chapterListUrlAttr;
        private String waitForSelector;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReplaceRule {
        private String pattern;
        private String replacement;
    }
} 