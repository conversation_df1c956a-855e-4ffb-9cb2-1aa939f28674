package com.shanhai.common.core.annotation;

import java.lang.annotation.*;

/**
 * ACTable 增强注解
 * <p>
 * 提供额外的表结构配置功能
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ActableEnhanced {

    /**
     * 表注释
     */
    String comment() default "";

    /**
     * 存储引擎
     */
    String engine() default "InnoDB";

    /**
     * 字符集
     */
    String charset() default "utf8mb4";

    /**
     * 排序规则
     */
    String collate() default "utf8mb4_unicode_ci";

    /**
     * 是否启用行格式压缩
     */
    boolean compressed() default false;

    /**
     * 自增起始值
     */
    long autoIncrementStart() default 1;

    /**
     * 分区类型
     */
    PartitionType partitionType() default PartitionType.NONE;

    /**
     * 分区字段
     */
    String partitionField() default "";

    /**
     * 分区数量
     */
    int partitionCount() default 0;

    /**
     * 是否启用审计字段
     */
    boolean enableAudit() default true;

    /**
     * 是否启用软删除
     */
    boolean enableSoftDelete() default true;

    /**
     * 是否启用乐观锁
     */
    boolean enableOptimisticLock() default true;

    /**
     * 索引定义
     */
    IndexDef[] indexes() default {};

    /**
     * 唯一约束定义
     */
    UniqueDef[] uniques() default {};

    /**
     * 分区类型枚举
     */
    enum PartitionType {
        NONE,           // 无分区
        RANGE,          // 范围分区
        LIST,           // 列表分区
        HASH,           // 哈希分区
        KEY             // 键分区
    }

    /**
     * 索引定义注解
     */
    @Target({})
    @Retention(RetentionPolicy.RUNTIME)
    @interface IndexDef {
        /**
         * 索引名称
         */
        String name();

        /**
         * 索引字段
         */
        String[] columns();

        /**
         * 索引类型
         */
        IndexType type() default IndexType.NORMAL;

        /**
         * 索引方法
         */
        IndexMethod method() default IndexMethod.BTREE;

        /**
         * 索引注释
         */
        String comment() default "";

        enum IndexType {
            NORMAL,     // 普通索引
            UNIQUE,     // 唯一索引
            FULLTEXT    // 全文索引
        }

        enum IndexMethod {
            BTREE,      // B树索引
            HASH        // 哈希索引
        }
    }

    /**
     * 唯一约束定义注解
     */
    @Target({})
    @Retention(RetentionPolicy.RUNTIME)
    @interface UniqueDef {
        /**
         * 约束名称
         */
        String name();

        /**
         * 约束字段
         */
        String[] columns();

        /**
         * 约束注释
         */
        String comment() default "";
    }
}
