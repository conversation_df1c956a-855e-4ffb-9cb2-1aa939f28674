package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.RuleChapter;
import com.shanhai.common.crawler.repository.ChapterListMapper;
import com.shanhai.common.crawler.service.ChapterListService;
import org.springframework.stereotype.Service;

/**
 * 章节列表页解析配置Service实现
 */
@Service
public class ChapterListServiceImpl extends ServiceImpl<ChapterListMapper, RuleChapter> implements ChapterListService {
} 