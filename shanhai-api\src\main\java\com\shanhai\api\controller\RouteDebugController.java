package com.shanhai.api.controller;

import com.shanhai.common.core.result.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路由调试控制器
 * 用于显示所有注册的路由信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/debug")
public class RouteDebugController {

    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    /**
     * 获取所有注册的路由
     * 
     * @return 路由列表
     */
    @GetMapping("/routes")
    public R<List<String>> getAllRoutes() {
        log.info("获取所有注册的路由");
        
        List<String> routes = new ArrayList<>();
        
        try {
            Map<RequestMappingInfo, ?> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
            
            for (RequestMappingInfo mappingInfo : handlerMethods.keySet()) {
                PatternsRequestCondition patternsCondition = mappingInfo.getPatternsCondition();
                if (patternsCondition != null) {
                    for (String pattern : patternsCondition.getPatterns()) {
                        String method = mappingInfo.getMethodsCondition().getMethods().isEmpty() 
                            ? "ALL" 
                            : mappingInfo.getMethodsCondition().getMethods().iterator().next().name();
                        
                        String route = method + " " + pattern;
                        routes.add(route);
                        log.debug("发现路由: {}", route);
                    }
                }
            }
            
            log.info("总共发现 {} 个路由", routes.size());
            return R.ok(routes);
            
        } catch (Exception e) {
            log.error("获取路由信息失败", e);
            return R.fail("获取路由信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取应用信息
     * 
     * @return 应用信息
     */
    @GetMapping("/info")
    public R<Map<String, Object>> getAppInfo() {
        log.info("获取应用信息");
        
        Map<String, Object> info = new HashMap<>();
        info.put("name", "山海小说爬虫系统");
        info.put("version", "1.0.1");
        info.put("description", "提供小说爬虫相关的 REST API 接口");
        info.put("timestamp", System.currentTimeMillis());
        
        return R.ok(info);
    }
} 