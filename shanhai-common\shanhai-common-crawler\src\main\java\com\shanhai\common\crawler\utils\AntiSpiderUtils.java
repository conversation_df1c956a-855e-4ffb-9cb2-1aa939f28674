package com.shanhai.common.crawler.utils;

import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;

/**
 * 反爬虫工具类
 * <p>
 * 提供User-Agent、代理、延迟等反爬虫策略的应用方法。
 *
 * <AUTHOR>
 */
@Slf4j
public class AntiSpiderUtils {
    /**
     * 应用反爬虫策略到Jsoup连接对象
     * <p>
     * 包括User-Agent、代理、请求延迟等。
     *
     * @param conn   Jsoup连接对象
     * @param config 爬虫规则配置
     */
    public static void apply(Connection conn, NovelCrawlerRule config) {
        if (config == null || config.getRuleAntiSpider() == null) {
            return;
        }
        if (config.getUserAgent() != null) {
            conn.userAgent(config.getUserAgent());
        }
        if (config.getRuleAntiSpider().getProxyList() != null && !config.getRuleAntiSpider().getProxyList().isEmpty()) {
            log.debug("应用代理策略");
        }
        if (config.getRuleAntiSpider().getMinDelayMs() != null && config.getRuleAntiSpider().getMaxDelayMs() != null) {
            try {
                long delay = config.getRuleAntiSpider().getMinDelayMs() +
                        (long) (Math.random() * (config.getRuleAntiSpider().getMaxDelayMs() - config.getRuleAntiSpider().getMinDelayMs()));
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
} 