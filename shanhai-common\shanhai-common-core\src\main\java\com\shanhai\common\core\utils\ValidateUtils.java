package com.shanhai.common.core.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.collection.CollUtil;

import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 验证工具类
 * 
 * <AUTHOR>
 */
public class ValidateUtils {

    /**
     * URL 正则表达式
     */
    private static final String URL_REGEX = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
    
    /**
     * 邮箱正则表达式
     */
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    
    /**
     * 手机号正则表达式
     */
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
    
    /**
     * IP地址正则表达式
     */
    private static final String IP_REGEX = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

    /**
     * 检查字符串是否为空
     * 
     * @param str 字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return StrUtil.isEmpty(str);
    }

    /**
     * 检查字符串是否不为空
     * 
     * @param str 字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return StrUtil.isNotEmpty(str);
    }

    /**
     * 检查字符串是否为空白
     * 
     * @param str 字符串
     * @return 是否为空白
     */
    public static boolean isBlank(String str) {
        return StrUtil.isBlank(str);
    }

    /**
     * 检查字符串是否不为空白
     * 
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return StrUtil.isNotBlank(str);
    }

    /**
     * 检查集合是否为空
     * 
     * @param collection 集合
     * @return 是否为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return CollUtil.isEmpty(collection);
    }

    /**
     * 检查集合是否不为空
     * 
     * @param collection 集合
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return CollUtil.isNotEmpty(collection);
    }

    /**
     * 检查Map是否为空
     * 
     * @param map Map对象
     * @return 是否为空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return CollUtil.isEmpty(map);
    }

    /**
     * 检查Map是否不为空
     * 
     * @param map Map对象
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return CollUtil.isNotEmpty(map);
    }

    /**
     * 检查对象是否为null
     * 
     * @param obj 对象
     * @return 是否为null
     */
    public static boolean isNull(Object obj) {
        return obj == null;
    }

    /**
     * 检查对象是否不为null
     * 
     * @param obj 对象
     * @return 是否不为null
     */
    public static boolean isNotNull(Object obj) {
        return obj != null;
    }

    /**
     * 验证URL格式
     * 
     * @param url URL字符串
     * @return 是否为有效URL
     */
    public static boolean isValidUrl(String url) {
        if (isBlank(url)) {
            return false;
        }
        return ReUtil.isMatch(URL_REGEX, url);
    }

    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱字符串
     * @return 是否为有效邮箱
     */
    public static boolean isValidEmail(String email) {
        if (isBlank(email)) {
            return false;
        }
        return ReUtil.isMatch(EMAIL_REGEX, email);
    }

    /**
     * 验证手机号格式
     * 
     * @param phone 手机号字符串
     * @return 是否为有效手机号
     */
    public static boolean isValidPhone(String phone) {
        if (isBlank(phone)) {
            return false;
        }
        return ReUtil.isMatch(PHONE_REGEX, phone);
    }

    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址字符串
     * @return 是否为有效IP地址
     */
    public static boolean isValidIp(String ip) {
        if (isBlank(ip)) {
            return false;
        }
        return ReUtil.isMatch(IP_REGEX, ip);
    }

    /**
     * 验证字符串长度
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否在指定长度范围内
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            return false;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证数字范围
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return 是否在指定范围内
     */
    public static boolean isInRange(Number value, Number min, Number max) {
        if (value == null || min == null || max == null) {
            return false;
        }
        double val = value.doubleValue();
        double minVal = min.doubleValue();
        double maxVal = max.doubleValue();
        return val >= minVal && val <= maxVal;
    }

    /**
     * 验证正则表达式
     * 
     * @param str 字符串
     * @param regex 正则表达式
     * @return 是否匹配
     */
    public static boolean matches(String str, String regex) {
        if (isBlank(str) || isBlank(regex)) {
            return false;
        }
        return ReUtil.isMatch(regex, str);
    }

    /**
     * 验证CSS选择器格式（简单验证）
     * 
     * @param selector CSS选择器
     * @return 是否为有效选择器
     */
    public static boolean isValidCssSelector(String selector) {
        if (isBlank(selector)) {
            return false;
        }
        // 简单的CSS选择器验证，可以根据需要扩展
        return selector.matches("^[a-zA-Z0-9\\s\\-_#.\\[\\]:()>+~*=\"']+$");
    }

    /**
     * 验证XPath表达式格式（简单验证）
     * 
     * @param xpath XPath表达式
     * @return 是否为有效XPath
     */
    public static boolean isValidXPath(String xpath) {
        if (isBlank(xpath)) {
            return false;
        }
        // 简单的XPath验证，可以根据需要扩展
        return xpath.matches("^[a-zA-Z0-9\\s\\-_/@\\[\\]:()=\"'\\*\\.]+$");
    }

    /**
     * 验证JSON Path表达式格式（简单验证）
     * 
     * @param jsonPath JSON Path表达式
     * @return 是否为有效JSON Path
     */
    public static boolean isValidJsonPath(String jsonPath) {
        if (isBlank(jsonPath)) {
            return false;
        }
        // 简单的JSON Path验证，可以根据需要扩展
        return jsonPath.matches("^\\$[a-zA-Z0-9\\s\\-_\\[\\]:()=\"'\\*\\.]+$");
    }

    /**
     * 验证超时时间（毫秒）
     * 
     * @param timeout 超时时间
     * @return 是否为有效超时时间
     */
    public static boolean isValidTimeout(Integer timeout) {
        if (timeout == null) {
            return false;
        }
        return timeout > 0 && timeout <= 300000; // 0-5分钟
    }

    /**
     * 验证延迟时间（毫秒）
     * 
     * @param delay 延迟时间
     * @return 是否为有效延迟时间
     */
    public static boolean isValidDelay(Integer delay) {
        if (delay == null) {
            return false;
        }
        return delay >= 0 && delay <= 60000; // 0-1分钟
    }

    /**
     * 验证User-Agent字符串
     * 
     * @param userAgent User-Agent字符串
     * @return 是否为有效User-Agent
     */
    public static boolean isValidUserAgent(String userAgent) {
        if (isBlank(userAgent)) {
            return false;
        }
        // User-Agent通常包含浏览器信息，长度合理即可
        return userAgent.length() >= 10 && userAgent.length() <= 500;
    }
}
