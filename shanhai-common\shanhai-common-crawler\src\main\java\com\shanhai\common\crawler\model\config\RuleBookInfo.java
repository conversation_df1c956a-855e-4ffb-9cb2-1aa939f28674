package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 书籍详情页解析配置
 * <p>
 * 用于描述如何从详情页提取书籍各项详细信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_rule_book_info")
@TableName("crawler_rule_book_info")
public class RuleBookInfo extends BaseEntity {
    /**
     * 书名选择器
     */
    @Column(name = "name_selector", comment = "书名选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String nameSelector;

    /**
     * 作者选择器
     */
    @Column(name = "author_selector", comment = "作者选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String authorSelector;

    /**
     * 封面选择器
     */
    @Column(name = "cover_selector", comment = "封面选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String coverSelector;

    /**
     * 简介选择器
     */
    @Column(name = "intro_selector", comment = "简介选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String introSelector;

    /**
     * 分类选择器
     */
    @Column(name = "category_selector", comment = "分类选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String categorySelector;

    /**
     * 字数选择器
     */
    @Column(name = "word_count_selector", comment = "字数选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String wordCountSelector;

    /**
     * 最新章节选择器
     */
    @Column(name = "last_chapter_selector", comment = "最新章节选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String lastChapterSelector;
    /**
     * 状态选择器
     */
    @Column(name = "status_selector", comment = "状态选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String statusSelector;

    /**
     * 更新时间选择器
     */
    @Column(name = "update_time_selector", comment = "更新时间选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String updateTimeSelector;

    /**
     * 章节目录页URL选择器
     */
    @Column(name = "chapter_list_url_selector", comment = "章节目录页URL选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String chapterListUrlSelector;

    /**
     * 章节目录页URL属性名
     */
    @Column(name = "chapter_list_url_attr", comment = "章节目录页URL属性名", type = MySqlTypeConstant.VARCHAR, length = 50, isNull = true, defaultValue = "href")
    private String chapterListUrlAttr;

    /**
     * 等待元素选择器（可选）
     */
    @Column(name = "wait_for_selector", comment = "等待元素选择器", type = MySqlTypeConstant.VARCHAR, length = 200, isNull = true)
    private String waitForSelector;

    /**
     * 关联主表ID
     */
    @Column(name = "rule_id", comment = "关联主表ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleId;
} 