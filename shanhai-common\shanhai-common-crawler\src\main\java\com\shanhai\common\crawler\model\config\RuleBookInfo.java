package com.shanhai.common.crawler.model.config;

import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 书籍详情页解析配置
 * <p>
 * 用于描述如何从详情页提取书籍各项详细信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleBookInfo extends BaseEntity {
    /**
     * 书名选择器
     */
    private String nameSelector;
    /**
     * 作者选择器
     */
    private String authorSelector;
    /**
     * 封面选择器
     */
    private String coverSelector;
    /**
     * 简介选择器
     */
    private String introSelector;
    /**
     * 分类选择器
     */
    private String categorySelector;
    /**
     * 字数选择器
     */
    private String wordCountSelector;
    /**
     * 最新章节选择器
     */
    private String lastChapterSelector;
    /**
     * 状态选择器
     */
    private String statusSelector;
    /**
     * 更新时间选择器
     */
    private String updateTimeSelector;
    /**
     * 章节目录页URL选择器
     */
    private String chapterListUrlSelector;
    /**
     * 章节目录页URL属性名
     */
    private String chapterListUrlAttr;
    /**
     * 等待元素选择器（可选）
     */
    private String waitForSelector;
    /**
     * 关联主表ID
     */
    private String ruleId;
} 