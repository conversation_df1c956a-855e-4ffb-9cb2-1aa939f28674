package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 站点基础信息配置
 * <p>
 * 用于描述目标站点的基础属性、请求头、<PERSON><PERSON>、超时等参数。
 * 线程安全：仅为数据结构，线程安全。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceInfo {
    /** 站点名称 */
    @NotBlank(message = "站点名称不能为空")
    private String sourceName;
    /** 站点URL */
    @NotBlank(message = "站点URL不能为空")
    private String sourceUrl;
    /** User-Agent配置（可选） */
    private String userAgent;
    /** 请求头配置（可选） */
    private Map<String, String> headers;
    /** Cookie配置（可选） */
    private Map<String, String> cookies;
    /** 超时时间（毫秒，可选） */
    private Integer timeout;
} 