package com.shanhai.common.crawler.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterListConfig {
    private String chapterItemSelector;
    private String chapterNameSelector;
    private String chapterUrlSelector;
    private String chapterUrlAttr;
    private Boolean reverseOrder;
    private Integer maxChapters;
    private String waitForSelector;
} 