package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.shanhai.common.core.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 小说爬虫规则主类
 * <p>
 * 作为所有规则配置的入口，聚合各子配置。支持配置校验和业务规则检查。
 * 线程安全：仅为数据结构，线程安全。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "crawler_rule")
@TableName("crawler_rule")
public class NovelCrawlerRule extends BaseEntity {
    /**
     * 站点名称
     */
    @NotBlank(message = "站点名称不能为空")
    @Column(name = "source_name", comment = "站点名称", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)
    private String sourceName;

    /**
     * 站点主页URL
     */
    @NotBlank(message = "站点URL不能为空")
    @Column(name = "source_url", comment = "站点主页URL", type = MySqlTypeConstant.VARCHAR, length = 500, isNull = false)
    private String sourceUrl;

    /**
     * User-Agent配置（可选）
     */
    @Column(name = "user_agent", comment = "User-Agent配置", type = MySqlTypeConstant.TEXT, isNull = true)
    private String userAgent;

    /**
     * 请求头配置（可选）
     */
    @Column(name = "headers", comment = "请求头配置(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private Map<String, String> headers;

    /**
     * Cookie配置（可选）
     */
    @Column(name = "cookies", comment = "Cookie配置(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private Map<String, String> cookies;

    /**
     * 超时时间（毫秒，可选）
     */
    @Column(name = "timeout", comment = "超时时间(毫秒)", type = MySqlTypeConstant.INT, length = 11, isNull = true, defaultValue = "10000")
    private Integer timeout;

    /**
     * 采集模式（API/HTML/SELENIUM/AUTO）
     */
    @NotBlank(message = "采集模式不能为空")
    @Column(name = "mode", comment = "采集模式(API/HTML/SELENIUM/AUTO)", type = MySqlTypeConstant.VARCHAR, length = 20, isNull = false, defaultValue = "HTML")
    private String mode;

    /**
     * 搜索配置ID
     */
    @Column(name = "rule_search_id", comment = "搜索配置ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleSearchId;

    /**
     * 书籍详情配置ID
     */
    @Column(name = "rule_book_info_id", comment = "书籍详情配置ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleBookInfoId;

    /**
     * 章节列表配置ID
     */
    @Column(name = "rule_chapter_id", comment = "章节列表配置ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleChapterId;

    /**
     * 正文段落配置ID
     */
    @Column(name = "rule_content_id", comment = "章节内容配置ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleContentId;

    /**
     * 反爬虫配置ID
     */
    @Column(name = "rule_anti_spider_id", comment = "反爬虫配置ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleAntiSpiderId;

    // 以下字段不存储到数据库，通过关联查询获取
    @NotNull(message = "搜索配置不能为空")
    private transient RuleSearch ruleSearch;

    @NotNull(message = "书籍详情配置不能为空")
    private transient RuleBookInfo ruleBookInfo;

    @NotNull(message = "章节列表配置不能为空")
    private transient RuleChapter ruleChapter;

    @NotNull(message = "章节内容配置不能为空")
    private transient RuleContent ruleContent;

    private transient RuleAntiSpider ruleAntiSpider;
} 