# 日志配置
logging:
  level:
    # 应用日志级别
    com.shanhai: DEBUG
    # Spring Web 日志级别
    org.springframework.web: DEBUG
    # Spring Boot 日志级别
    org.springframework.boot: INFO
    # 根日志级别
    root: INFO
  pattern:
    # 控制台输出格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    # 文件输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    # 日志文件路径
    name: logs/shanhai.log
    # 日志文件大小限制
    max-size: 10MB
    # 日志文件保留天数
    max-history: 30 