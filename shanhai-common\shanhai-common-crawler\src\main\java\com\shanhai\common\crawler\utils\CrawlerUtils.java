package com.shanhai.common.crawler.utils;

import com.shanhai.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 爬虫工具类
 * <p>
 * 提供爬虫相关的通用工具方法
 *
 * <AUTHOR>
 */
@Slf4j
public class CrawlerUtils {

    private static final Pattern URL_PATTERN = Pattern.compile("https?://[^\\s/$.?#].[^\\s]*");
    private static final Pattern DOMAIN_PATTERN = Pattern.compile("https?://([^/]+)");

    /**
     * 根据选择器和属性提取文本或属性值
     */
    public static String extractText(Element element, String selector, String attr) {
        if (element == null || StringUtils.isBlank(selector)) {
            return "";
        }

        Elements elements = element.select(selector);
        if (elements.isEmpty()) {
            return "";
        }

        Element target = elements.first();
        if (StringUtils.isBlank(attr)) {
            return target.text().trim();
        } else {
            return target.attr(attr).trim();
        }
    }

    /**
     * 根据选择器提取文本
     */
    public static String extractText(Element element, String selector) {
        return extractText(element, selector, null);
    }

    /**
     * 根据选择器提取属性值
     */
    public static String extractAttr(Element element, String selector, String attr) {
        return extractText(element, selector, attr);
    }

    /**
     * 提取所有匹配元素的文本
     */
    public static List<String> extractAllText(Element element, String selector) {
        List<String> results = new ArrayList<>();
        if (element == null || StringUtils.isBlank(selector)) {
            return results;
        }

        Elements elements = element.select(selector);
        for (Element el : elements) {
            String text = el.text().trim();
            if (StringUtils.isNotBlank(text)) {
                results.add(text);
            }
        }
        return results;
    }

    /**
     * 构建完整URL
     */
    public static String buildFullUrl(String baseUrl, String relativeUrl) {
        if (StringUtils.isBlank(relativeUrl)) {
            return "";
        }

        // 如果已经是完整URL，直接返回
        if (relativeUrl.startsWith("http://") || relativeUrl.startsWith("https://")) {
            return relativeUrl;
        }

        if (StringUtils.isBlank(baseUrl)) {
            return relativeUrl;
        }

        try {
            URL base = new URL(baseUrl);
            URL full = new URL(base, relativeUrl);
            return full.toString();
        } catch (MalformedURLException e) {
            log.warn("构建完整URL失败: baseUrl={}, relativeUrl={}", baseUrl, relativeUrl);
            return relativeUrl;
        }
    }

    /**
     * 从URL中提取域名
     */
    public static String extractDomain(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        Matcher matcher = DOMAIN_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 清理文本内容
     */
    public static String cleanText(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }

        return text.replaceAll("\\s+", " ")  // 多个空白字符替换为单个空格
                .replaceAll("&nbsp;", " ")   // HTML空格实体
                .replaceAll("&amp;", "&")    // HTML &实体
                .replaceAll("&lt;", "<")     // HTML <实体
                .replaceAll("&gt;", ">")     // HTML >实体
                .replaceAll("&quot;", "\"")  // HTML "实体
                .trim();
    }

    /**
     * 移除HTML标签
     */
    public static String removeHtmlTags(String html) {
        if (StringUtils.isBlank(html)) {
            return "";
        }
        return html.replaceAll("<[^>]+>", "").trim();
    }

    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        return URL_PATTERN.matcher(url).matches();
    }

    /**
     * 从文本中提取所有URL
     */
    public static List<String> extractUrls(String text) {
        List<String> urls = new ArrayList<>();
        if (StringUtils.isBlank(text)) {
            return urls;
        }

        Matcher matcher = URL_PATTERN.matcher(text);
        while (matcher.find()) {
            urls.add(matcher.group());
        }
        return urls;
    }

    /**
     * 处理搜索关键词
     */
    public static String processKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return "";
        }

        return keyword.trim()
                .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                .replaceAll("[\"'`]", "") // 移除引号
                .replaceAll("[<>]", "");  // 移除尖括号
    }

    /**
     * 构建搜索URL
     */
    public static String buildSearchUrl(String urlTemplate, String keyword) {
        if (StringUtils.isBlank(urlTemplate) || StringUtils.isBlank(keyword)) {
            return "";
        }

        String processedKeyword = processKeyword(keyword);
        return urlTemplate.replace("{keyword}", processedKeyword)
                .replace("{KEYWORD}", processedKeyword)
                .replace("${keyword}", processedKeyword);
    }

    /**
     * 检查元素是否存在
     */
    public static boolean hasElement(Document doc, String selector) {
        return doc != null && StringUtils.isNotBlank(selector) && !doc.select(selector).isEmpty();
    }

    /**
     * 安全地获取元素文本
     */
    public static String safeGetText(Document doc, String selector) {
        if (doc == null || StringUtils.isBlank(selector)) {
            return "";
        }

        Elements elements = doc.select(selector);
        return elements.isEmpty() ? "" : cleanText(elements.first().text());
    }

    /**
     * 安全地获取元素属性
     */
    public static String safeGetAttr(Document doc, String selector, String attr) {
        if (doc == null || StringUtils.isBlank(selector) || StringUtils.isBlank(attr)) {
            return "";
        }

        Elements elements = doc.select(selector);
        return elements.isEmpty() ? "" : elements.first().attr(attr).trim();
    }

    /**
     * 计算文本相似度（简单实现）
     */
    public static double calculateSimilarity(String text1, String text2) {
        if (StringUtils.isBlank(text1) || StringUtils.isBlank(text2)) {
            return 0.0;
        }

        if (text1.equals(text2)) {
            return 1.0;
        }

        // 简单的字符匹配相似度计算
        int maxLength = Math.max(text1.length(), text2.length());
        int commonChars = 0;

        for (int i = 0; i < Math.min(text1.length(), text2.length()); i++) {
            if (text1.charAt(i) == text2.charAt(i)) {
                commonChars++;
            }
        }

        return (double) commonChars / maxLength;
    }

    /**
     * 生成唯一标识符
     */
    public static String generateId(String sourceName, String bookName, String author) {
        return String.format("%s_%s_%s", 
            StringUtils.isBlank(sourceName) ? "unknown" : sourceName,
            StringUtils.isBlank(bookName) ? "unknown" : bookName,
            StringUtils.isBlank(author) ? "unknown" : author)
            .replaceAll("[^a-zA-Z0-9_\\u4e00-\\u9fa5]", "_")
            .replaceAll("_{2,}", "_");
    }

    /**
     * 检查是否为有效的章节标题
     */
    public static boolean isValidChapterTitle(String title) {
        if (StringUtils.isBlank(title)) {
            return false;
        }

        title = title.trim();
        
        // 检查是否包含章节关键词
        return title.matches(".*[第章节回卷部篇].*") || 
               title.matches(".*Chapter.*") ||
               title.matches(".*\\d+.*");
    }

    /**
     * 从章节标题中提取章节号
     */
    public static String extractChapterNumber(String title) {
        if (StringUtils.isBlank(title)) {
            return "";
        }

        // 匹配数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(title);
        
        if (matcher.find()) {
            return matcher.group();
        }
        
        return "";
    }
}
