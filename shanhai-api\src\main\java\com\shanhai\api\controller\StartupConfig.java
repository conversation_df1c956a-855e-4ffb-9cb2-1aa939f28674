package com.shanhai.api.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Map;

/**
 * 启动配置类
 * 在应用启动后执行一些初始化操作
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class StartupConfig implements CommandLineRunner {

    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 山海小说爬虫系统启动完成 ===");
        
        // 检查路由注册情况
        checkRoutes();
        
        log.info("=== 系统初始化完成 ===");
    }

    /**
     * 检查路由注册情况
     */
    private void checkRoutes() {
        try {
            Map<RequestMappingInfo, ?> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
            log.info("总共注册了 {} 个路由处理器", handlerMethods.size());
            
            // 检查关键路由是否存在
            boolean hasSearchRoute = false;
            boolean hasTestRoute = false;
            boolean hasDebugRoute = false;
            
            for (RequestMappingInfo mappingInfo : handlerMethods.keySet()) {
                if (mappingInfo.getPatternsCondition() != null) {
                    for (String pattern : mappingInfo.getPatternsCondition().getPatterns()) {
                        if (pattern.contains("/api/v1/novel/crawler/search")) {
                            hasSearchRoute = true;
                            log.info("✓ 搜索路由已注册: {}", pattern);
                        }
                        if (pattern.contains("/api/v1/test/health")) {
                            hasTestRoute = true;
                            log.info("✓ 测试路由已注册: {}", pattern);
                        }
                        if (pattern.contains("/api/v1/debug/routes")) {
                            hasDebugRoute = true;
                            log.info("✓ 调试路由已注册: {}", pattern);
                        }
                    }
                }
            }
            
            if (!hasSearchRoute) {
                log.warn("⚠ 搜索路由未注册，请检查 NovelCrawlerController 是否正确加载");
            }
            if (!hasTestRoute) {
                log.warn("⚠ 测试路由未注册，请检查 TestController 是否正确加载");
            }
            if (!hasDebugRoute) {
                log.warn("⚠ 调试路由未注册，请检查 RouteDebugController 是否正确加载");
            }
            
        } catch (Exception e) {
            log.error("检查路由注册情况失败", e);
        }
    }
} 