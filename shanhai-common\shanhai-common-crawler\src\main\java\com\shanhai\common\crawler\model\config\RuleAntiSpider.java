package com.shanhai.common.crawler.model.config;

import lombok.*;
import com.shanhai.common.core.model.BaseEntity;

import java.util.List;

/**
 * 反爬虫配置
 * <p>
 * 用于描述User-Agent、代理、延迟等反爬虫参数。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleAntiSpider extends BaseEntity {
    /**
     * User-Agent列表（可选）
     */
    private List<String> userAgents;
    /**
     * 代理IP列表（可选）
     */
    private List<String> proxyList;
    /**
     * 最小请求延迟（毫秒，可选）
     */
    private Integer minDelayMs;
    /**
     * 最大请求延迟（毫秒，可选）
     */
    private Integer maxDelayMs;
    /** 关联主表ID */
    private String ruleId;
}