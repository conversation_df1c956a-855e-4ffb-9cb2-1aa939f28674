package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.*;
import com.shanhai.common.core.model.BaseEntity;

import java.util.List;

/**
 * 反爬虫配置
 * <p>
 * 用于描述User-Agent、代理、延迟等反爬虫参数。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "crawler_rule_anti_spider")
@TableName("crawler_rule_anti_spider")
public class RuleAntiSpider extends BaseEntity {
    /**
     * User-Agent列表（可选）
     */
    @Column(name = "user_agents", comment = "User-Agent列表(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private String userAgentsJson;

    /**
     * 代理IP列表（可选）
     */
    @Column(name = "proxy_list", comment = "代理IP列表(JSON格式)", type = MySqlTypeConstant.TEXT, isNull = true)
    private String proxyListJson;

    /**
     * 最小请求延迟（毫秒，可选）
     */
    @Column(name = "min_delay_ms", comment = "最小请求延迟(毫秒)", type = MySqlTypeConstant.INT, length = 11, isNull = true, defaultValue = "1000")
    private Integer minDelayMs;

    /**
     * 最大请求延迟（毫秒，可选）
     */
    @Column(name = "max_delay_ms", comment = "最大请求延迟(毫秒)", type = MySqlTypeConstant.INT, length = 11, isNull = true, defaultValue = "3000")
    private Integer maxDelayMs;

    /** 关联主表ID */
    @Column(name = "rule_id", comment = "关联主表ID", type = MySqlTypeConstant.VARCHAR, length = 32, isNull = true)
    private String ruleId;

    // 以下字段不存储到数据库，通过JSON解析获取
    private transient List<String> userAgents;
    private transient List<String> proxyList;
}